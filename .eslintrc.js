module.exports = {
  root: true,
  parserOptions: {
    parser: 'babel-eslint',
    sourceType: 'module'
  },
  env: {
    browser: true,
    node: true,
    es6: true,
  },
  extends: [
    'plugin:vue/essential',
    'eslint:recommended'
  ],
  plugins: [
    'vue'
  ],
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'no-unused-vars': 'off',
    'no-undef': 'off',
    'no-redeclare': 'off',
    'no-prototype-builtins': 'off',
    'no-constant-condition': 'off',
    'no-useless-escape': 'off',
    'no-mixed-spaces-and-tabs': 'off',
    'no-dupe-keys': 'off',
    'vue/multi-word-component-names': 'off',
    'vue/no-unused-components': 'off',
    'vue/no-mutating-props': 'off',
    'vue/no-unused-vars': 'off',
    'vue/no-dupe-keys': 'off',
    'vue/no-parsing-error': 'off',
    'vue/no-reserved-component-names': 'off'
  },
  globals: {
    '$': true,
    'require': true
  }
}
