# 开票功能实现说明

## 功能概述

在充值订单管理页面中增加了开票相关功能，包括：
- 显示订单开票次数
- 上传发票文件（PDF格式）
- 查看开票详情信息
- 下载已上传的发票

## 实现的文件

### 1. API接口扩展
- `src/api/recharge_order.js` - 新增开票相关接口
  - `getInvoiceDetail(orderId)` - 获取订单开票详情
  - `uploadInvoice(orderId, data)` - 上传发票文件
  - `downloadInvoice(orderId, invoiceId)` - 下载发票文件

### 2. 新增组件
- `src/components/Upload/PdfUpload.vue` - PDF文件上传组件
- `src/views/recharge_orders/components/InvoiceDetailDialog.vue` - 开票详情弹窗
- `src/views/recharge_orders/components/InvoiceUploadDialog.vue` - 发票上传弹窗

### 3. 页面扩展
- `src/views/recharge_orders/index.vue` - 充值订单列表页面扩展

## 功能说明

### 充值订单列表新增列

1. **开票次数列**
   - 显示该订单的开票次数
   - 如果没有开票记录显示 "-"
   - 有开票记录显示 "X次"

2. **开票操作列**
   - 只有已支付的订单才显示操作按钮
   - 如果有开票申请但未上传发票：显示红色"上传发票"按钮
   - 如果已上传发票：显示蓝色"查看详情"按钮
   - 如果没有开票申请：显示 "-"

### 开票详情弹窗

显示内容：
- 开票基本信息（发票类型、抬头信息、开票金额等）
- 单位信息（如果是单位抬头）
- 开票历史记录（时间线形式）
- 发票文件下载链接

### 发票上传弹窗

功能：
- 显示开票申请信息
- PDF文件上传（限制10MB以内）
- 备注信息输入
- 上传进度显示

## 后端接口要求

需要后端提供以下接口：

### 1. 充值订单列表接口扩展
`GET /recharge_order`

返回数据需要包含：
```json
{
  "data": {
    "data": [
      {
        "id": 1,
        "order_no": "R202412100001",
        "order_amount": "100.00",
        "status": 1,
        "invoice_count": 2,
        "has_invoice_request": true,
        "invoice_uploaded": true,
        // ... 其他字段
      }
    ]
  }
}
```

### 2. 获取开票详情接口
`GET /recharge_order/{orderId}/invoice`

返回数据：
```json
{
  "code": 200,
  "data": {
    "invoice_type": 1,
    "header_type": 2,
    "header_name": "某某公司",
    "tax_number": "91110000000000000X",
    "invoice_content": "技术咨询服务",
    "invoice_amount": "100.00",
    "invoice_count": 2,
    "history": [
      {
        "id": 1,
        "status": 1,
        "invoice_file_url": "invoices/xxx.pdf",
        "created_at": "2024-12-10 10:00:00",
        "remark": "第一次开票"
      }
    ]
  }
}
```

### 3. 上传发票接口
`POST /recharge_order/{orderId}/invoice/upload`

请求数据：
```json
{
  "invoice_file_url": "invoices/xxx.pdf",
  "invoice_file_name": "发票.pdf",
  "remark": "备注信息"
}
```

### 4. 下载发票接口
`GET /recharge_order/{orderId}/invoice/{invoiceId}/download`

返回PDF文件流

## 使用说明

1. **查看开票信息**
   - 在充值订单列表中，可以看到每个订单的开票次数
   - 点击"查看详情"按钮可以查看完整的开票信息

2. **上传发票**
   - 对于有开票申请但未上传发票的订单，会显示红色"上传发票"按钮
   - 点击按钮打开上传弹窗，选择PDF文件进行上传
   - 支持添加备注信息

3. **下载发票**
   - 在开票详情弹窗中，可以下载已上传的发票文件

## 注意事项

1. **文件格式限制**
   - 只支持PDF格式的发票文件
   - 文件大小限制为10MB以内

2. **权限控制**
   - 只有管理员可以上传发票
   - 用户只能查看自己的开票记录

3. **状态提醒**
   - 有待上传发票的订单会显示红色按钮提醒
   - 上传成功后按钮变为蓝色"查看详情"

## 样式说明

- 开票次数显示为蓝色加粗
- 上传发票按钮为红色，突出提醒
- 查看详情按钮为蓝色
- 无开票信息显示为灰色"-"

## 扩展性

该实现具有良好的扩展性，可以方便地添加：
- 开票申请功能
- 开票信息管理
- 批量开票操作
- 开票统计报表
