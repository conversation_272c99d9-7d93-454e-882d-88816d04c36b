const gulp = require('gulp')
const ftp = require('gulp-sftp-up5')

const config = {
  uploadTest: {
    remotePath: '',
    host: '',
    user: 'root',
    pass: '',
    port: 22,
    removeCurrentFolderFiles: true
  },
  uploadProd: {
    remotePath: '',
    host: '',
    user: 'root',
    pass: '',
    port: 22,
    removeCurrentFolderFiles: true
  },
  publicPath: '/dist/' // 本地程序编译好路径
}

/**
 * 部署到环境
 */
gulp.task('uploadTest', function(callback) {
  console.log('## 正在部署到测试服务器上')
  var dev = config.uploadTest
  return gulp.src('.' + config.publicPath + '**')
    .pipe(ftp(Object.assign(dev, { callback })))
})

gulp.task('upload', function(callback) {
  console.log('## 正在部署到正式服务器上')
  var dev = config.uploadProd
  return gulp.src('.' + config.publicPath + '**')
    .pipe(ftp(Object.assign(dev, { callback })))
})
