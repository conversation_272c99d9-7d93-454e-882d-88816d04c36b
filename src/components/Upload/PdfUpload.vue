<template>
  <div class="pdf-upload">
    <el-upload
      v-if="refresh"
      :ref="refName"
      v-loading="loading"
      :class="className"
      :data="{}"
      action=""
      :http-request="fnUploadRequest"
      accept=".pdf"
      :limit="limit"
      :multiple="false"
      :file-list="fileList"
      :show-file-list="showFileList"
      :before-upload="handleBeforeUpload"
      :on-exceed="handleExceed"
      :on-progress="handleProgress"
      :on-success="handleSuccess"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :on-error="handleError"
      :on-change="handleChange"
    >
      <slot>
        <el-button type="primary" icon="el-icon-upload">
          上传PDF发票
        </el-button>
      </slot>
    </el-upload>
    <div v-if="tips" class="upload-tips">
      <i class="el-icon-info"></i>
      {{ tips }}
    </div>
  </div>
</template>

<script>
import { getToken, OssKey } from '@/utils/auth'
import { getQiniuToken } from '@/api/qiniu'
import OSS from 'ali-oss'

export default {
  name: 'PdfUpload',
  props: {
    className: {
      type: String,
      default: ''
    },
    refName: {
      type: String,
      default: 'upload'
    },
    limit: {
      type: Number,
      default: 1
    },
    fileList: {
      type: Array,
      default: () => []
    },
    showFileList: {
      type: Boolean,
      default: true
    },
    tips: {
      type: String,
      default: '支持PDF格式，文件大小不超过10MB'
    }
  },
  data() {
    return {
      loading: false,
      refresh: true,
      oss: {}
    }
  },
  created() {
    this.getOssToken()
  },
  methods: {
    async getOssToken() {
      try {
        if (getToken(OssKey)) {
          this.oss = JSON.parse(getToken(OssKey))
        } else {
          const response = await getQiniuToken()
          if (response.code === 200) {
            this.oss = response.data.oss
          }
        }
      } catch (error) {
        console.error('获取OSS配置失败:', error)
      }
    },

    handleBeforeUpload(file) {
      // 检查文件类型
      const isPDF = file.type === 'application/pdf'
      if (!isPDF) {
        this.$message.error('只能上传PDF格式的文件!')
        return false
      }

      // 检查文件大小
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('文件大小不能超过10MB!')
        return false
      }

      this.loading = true
      this.$emit('handleBeforeUpload', file)
      return true
    },

    handleSuccess(res, file, fileList) {
      this.loading = false
      this.$emit('handleSuccess', res, file, fileList)
    },

    handlePreview(file) {
      this.$emit('handlePreview', file)
    },

    handleChange(file, fileList) {
      this.$emit('handleChange', file, fileList)
    },

    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 ${this.limit} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
      this.$emit('handleExceed', files, fileList)
    },

    handleRemove(file, fileList) {
      this.$emit('handleRemove', file, fileList)
    },

    handleError(err, file, fileList) {
      this.loading = false
      this.$message.error('上传失败，请重试')
      this.$emit('handleError', err, file, fileList)
    },

    handleProgress(event, file, fileList) {
      this.$emit('handleProgress', event, file, fileList)
    },

    async fnUploadRequest(options) {
      try {
        if (!this.oss.AccessKeyId) {
          await this.getOssToken()
        }

        const client = new OSS({
          region: 'oss-cn-beijing',
          accessKeyId: this.oss.AccessKeyId,
          accessKeySecret: this.oss.AccessKeySecret,
          stsToken: this.oss.SecurityToken,
          bucket: this.oss.BucketName
        })

        const date = `${new Date().getFullYear()}${(new Date().getMonth() + 1).toString().padStart(2, '0')}${new Date().getDate().toString().padStart(2, '0')}`
        const time = new Date().getTime()
        const filename = `invoices/${date}/${time}_${options.file.name.replace(/\s*/g, '')}`

        const result = await client.multipartUpload(filename, options.file, {
          progress: (p) => {
            this.$emit('elProgress', p)
          }
        })

        options.onSuccess({
          url: result.name,
          name: options.file.name
        })
      } catch (error) {
        console.error('上传失败:', error)
        options.onError(error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.pdf-upload {
  .upload-tips {
    margin-top: 8px;
    color: #909399;
    font-size: 12px;
    line-height: 1.5;

    .el-icon-info {
      margin-right: 4px;
    }
  }
}
</style>
