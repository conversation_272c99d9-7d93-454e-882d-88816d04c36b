<template>
  <el-dialog
    title="打折配置"
    :visible.sync="visible"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      ref="dataForm"
      :model="dataForm"
      :rules="dataRule"
      label-width="100px"
      v-loading="dataLoading"
      element-loading-text="加载配置中..."
      @keyup.enter.native="dataFormSubmitHandle()"
    >
      <el-form-item label="折扣率" prop="discount">
        <el-input-number
          v-model="dataForm.discount"
          :min="1"
          :max="100"
          :step="1"
          placeholder="请输入折扣率"
          style="width: 100%"
        />
        <div class="form-tip">折扣率范围：1-100，例如80表示8折</div>
      </el-form-item>
      <el-form-item label="开始时间" prop="start_time">
        <el-date-picker
          v-model="dataForm.start_time"
          type="datetime"
          placeholder="选择开始时间"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="end_time">
        <el-date-picker
          v-model="dataForm.end_time"
          type="datetime"
          placeholder="选择结束时间"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="启用状态" prop="enabled">
        <el-switch
          v-model="dataForm.enabled"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" :loading="loading" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { getDiscount, setDiscount } from '@/api/discount'

export default {
  name: 'DiscountConfig',
  data() {
    return {
      visible: false,
      loading: false,
      dataLoading: false,
      dataForm: {
        discount: 80,
        start_time: '',
        end_time: '',
        enabled: true
      },
      dataRule: {
        discount: [
          { required: true, message: '折扣率不能为空', trigger: 'blur' },
          { type: 'number', min: 1, max: 100, message: '折扣率必须在1-100之间', trigger: 'blur' }
        ],
        start_time: [
          { required: true, message: '开始时间不能为空', trigger: 'change' }
        ],
        end_time: [
          { required: true, message: '结束时间不能为空', trigger: 'change' },
          { validator: this.validateEndTime, trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.dataForm.resetFields()
        this.loadDiscountConfig()
      })
    },
    loadDiscountConfig() {
      this.dataLoading = true
      getDiscount({
        type: this.getItemType()
      })
        .then((response) => {
          if (response.code === 200 && response.data) {
            // 如果有配置数据，填充表单
            const data = response.data
            this.dataForm.discount = data.discount || 80
            this.dataForm.start_time = data.start_time || ''
            this.dataForm.end_time = data.end_time || ''
            this.dataForm.enabled = data.enabled !== undefined ? data.enabled : false
          } else {
            // 如果没有配置数据，使用默认值
            this.setDefaultValues()
          }
        })
        .catch(() => {
          // 获取失败时使用默认值
          this.setDefaultValues()
        })
        .finally(() => {
          this.dataLoading = false
        })
    },
    setDefaultValues() {
      this.dataForm.discount = 0.8
      this.dataForm.start_time = ''
      this.dataForm.end_time = ''
      this.dataForm.enabled = false
    },
    getDefaultStartTime() {
      const now = new Date()
      const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)
      return this.formatDateTime(tomorrow)
    },
    getDefaultEndTime() {
      const now = new Date()
      const nextMonth = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
      return this.formatDateTime(nextMonth)
    },
    validateEndTime(rule, value, callback) {
      if (value && this.dataForm.start_time) {
        if (new Date(value) <= new Date(this.dataForm.start_time)) {
          callback(new Error('结束时间必须大于开始时间'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    formatDateTime(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    dataFormSubmitHandle() {
      this.$refs.dataForm.validate((valid) => {
        if (!valid) {
          return false
        }
        this.loading = true
        setDiscount({
          ...this.dataForm,
          type: this.getItemType()
        })
          .then((response) => {
            this.$message.success('打折配置设置成功')
            this.visible = false
            this.$emit('refreshList')
          })
          .catch(() => {
            // 错误信息已在request拦截器中处理
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    getItemType() {
      // 根据当前路由判断类型
      const path = this.$route.path
      if (path.includes('course')) {
        return 'course'
      } else if (path.includes('plug')) {
        return 'plug'
      } else if (path.includes('goods')) {
        return 'goods'
      } else if (path.includes('shop')) {
        return 'goods'
      }
      return 'unknow'
    }
  }
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
</style>
