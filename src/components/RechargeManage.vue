<template>
  <el-dialog
    title="光子充值管理"
    :visible.sync="visible"
    width="1200px"
    top="5vh"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-tabs v-model="activeTab" type="card">
      <!-- 光子充值 -->
      <el-tab-pane label="光子充值" name="recharge">
        <el-form
          ref="rechargeForm"
          :model="rechargeForm"
          :rules="rechargeRules"
          label-width="100px"
          style="max-width: 500px"
        >
          <el-form-item label="用户手机号" prop="phone">
            <el-input
              v-model="rechargeForm.phone"
              placeholder="请输入用户手机号"
              clearable
            />
          </el-form-item>
          <el-form-item label="充值金额" prop="amount">
            <el-input-number
              v-model="rechargeForm.amount"
              :min="0.01"
              :max="999999.99"
              :precision="2"
              placeholder="请输入充值金额"
              style="width: 100%"
            />
            <div class="form-tip">充值金额范围：0.01 - 999999.99 光子</div>
          </el-form-item>
          <el-form-item label="备注信息" prop="remark">
            <el-input
              v-model="rechargeForm.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息（可选）"
              :maxlength="255"
              show-word-limit
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              :loading="rechargeLoading"
              @click="handleRecharge"
            >
              确认充值
            </el-button>
            <el-button @click="resetRechargeForm">重置</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <!-- 充值记录 -->
      <el-tab-pane label="充值记录" name="records">
        <div class="filter-container">
          <el-form :inline="true" :model="recordSearch">
            <el-form-item label="手机号">
              <el-input
                v-model="recordSearch.phone"
                placeholder="用户手机号"
                clearable
                @clear="getRecordList(1)"
                @keyup.enter.native="getRecordList(1)"
              />
            </el-form-item>
            <el-form-item label="订单号">
              <el-input
                v-model="recordSearch.order_no"
                placeholder="订单号"
                clearable
                @clear="getRecordList(1)"
                @keyup.enter.native="getRecordList(1)"
              />
            </el-form-item>
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="recordDateRange"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                @change="onRecordDateChange"
              />
            </el-form-item>
            <el-button icon="el-icon-search" @click="getRecordList(1)">
              搜索
            </el-button>
<!--            <el-button-->
<!--              type="success"-->
<!--              icon="el-icon-download"-->
<!--              :loading="exportLoading"-->
<!--              @click="handleExport"-->
<!--            >-->
<!--              导出-->
<!--            </el-button>-->
          </el-form>
        </div>

        <!-- 统计信息暂时取消展示 -->
        <!-- <div v-if="stats" class="stats-container">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-value">{{ getStatValue(stats.total_amount) }}</div>
                <div class="stat-label">总充值金额（光子）</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-value">{{ getStatValue(stats.total_count) }}</div>
                <div class="stat-label">总充值次数</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-value">{{ getStatValue(stats.average_amount) }}</div>
                <div class="stat-label">平均充值金额</div>
              </div>
            </el-col>
          </el-row>
        </div> -->

        <!-- 记录列表 -->
        <el-table
          v-loading="recordLoading"
          :data="recordList"
          border
          style="margin-top: 20px"
          max-height="400"
        >
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column prop="order_no" label="订单号" width="180" />
          <el-table-column label="用户信息" width="150">
            <template slot-scope="{ row }">
              <div>{{ getUserName(row.user) }}</div>
              <div style="color: #999; font-size: 12px">{{ getUserPhone(row.user) }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="order_amount" label="充值金额" width="120" align="right">
            <template slot-scope="{ row }">
              {{ row.order_amount }} 光子
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" min-width="150" />
          <el-table-column prop="created_at" label="充值时间" width="160" />
        </el-table>

        <!-- 分页 -->
        <pagination
          v-show="recordPages.total > 0"
          :total="recordPages.total"
          :page.sync="recordPages.current"
          :limit.sync="recordPages.limit"
          @pagination="getRecordList()"
        />
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script>
import { adminRecharge, getRechargeList, exportRecharge } from '@/api/recharge'
import Pagination from '@/components/Pagination'

export default {
  name: 'RechargeManage',
  components: { Pagination },
  data() {
    return {
      visible: false,
      activeTab: 'recharge',
      rechargeLoading: false,
      recordLoading: false,
      exportLoading: false,
      
      // 充值表单
      rechargeForm: {
        phone: '',
        amount: null,
        remark: ''
      },
      rechargeRules: {
        phone: [
          { required: true, message: '请输入用户手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号', trigger: 'blur' }
        ],
        amount: [
          { required: true, message: '请输入充值金额', trigger: 'blur' },
          { type: 'number', min: 0.01, max: 999999.99, message: '充值金额范围：0.01 - 999999.99', trigger: 'blur' }
        ]
      },

      // 记录查询
      recordSearch: {
        phone: '',
        order_no: '',
        start_time: '',
        end_time: ''
      },
      recordDateRange: [],
      recordList: [],
      recordPages: {
        total: 0,
        limit: 20,
        current: 1
      },
      stats: null
    }
  },
  methods: {
    init() {
      this.visible = true
      this.activeTab = 'recharge'
      this.resetRechargeForm()
      this.resetRecordSearch()
    },
    
    // 充值相关方法
    handleRecharge() {
      this.$refs.rechargeForm.validate((valid) => {
        if (!valid) return false
        
        this.rechargeLoading = true
        adminRecharge(this.rechargeForm)
          .then((response) => {
            this.$message.success('充值成功')
            this.resetRechargeForm()
            // 如果当前在记录页面，刷新记录
            if (this.activeTab === 'records') {
              this.getRecordList()
            }
          })
          .catch(() => {
            // 错误信息已在request拦截器中处理
          })
          .finally(() => {
            this.rechargeLoading = false
          })
      })
    },
    
    resetRechargeForm() {
      this.rechargeForm = {
        phone: '',
        amount: null,
        remark: ''
      }
      this.$nextTick(() => {
        if (this.$refs.rechargeForm) {
          this.$refs.rechargeForm.clearValidate()
        }
      })
    },

    // 记录相关方法
    getRecordList(page = this.recordPages.current) {
      if (this.recordLoading) return
      this.recordLoading = true
      if (page === 1) this.recordPages.current = page
      
      getRechargeList({
        page,
        limit: this.recordPages.limit,
        ...this.recordSearch
      })
        .then((response) => {
          if (response.code === 200) {
            this.recordList = response.data.list.data || []
            this.recordPages.total = response.data.list.total || 0
            this.stats = response.data.stats || null
          }
        })
        .catch(() => {
          // 错误信息已在request拦截器中处理
        })
        .finally(() => {
          this.recordLoading = false
        })
    },

    onRecordDateChange(value) {
      if (Array.isArray(value)) {
        this.recordSearch.start_time = value[0]
        this.recordSearch.end_time = value[1]
      } else {
        this.recordSearch.start_time = ''
        this.recordSearch.end_time = ''
        this.getRecordList(1)
      }
    },

    resetRecordSearch() {
      this.recordSearch = {
        phone: '',
        order_no: '',
        start_time: '',
        end_time: ''
      }
      this.recordDateRange = []
      this.recordPages.current = 1
    },

    handleExport() {
      this.exportLoading = true
      exportRecharge(this.recordSearch)
        .then((response) => {
          if (response.code === 200) {
            this.$message.success('导出成功')
            // 这里可以处理文件下载逻辑
            if (response.data.filename) {
              window.open(response.data.filename)
            }
          }
        })
        .catch(() => {
          // 错误信息已在request拦截器中处理
        })
        .finally(() => {
          this.exportLoading = false
        })
    },

    // 辅助方法
    getStatValue(value) {
      return value || '0.00'
    },

    getUserName(user) {
      return (user && user.name) || '-'
    },

    getUserPhone(user) {
      return (user && user.phone) || '-'
    }
  },
  
  watch: {
    activeTab(newTab) {
      if (newTab === 'records') {
        this.getRecordList(1)
      }
    }
  }
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.filter-container {
  margin-bottom: 20px;
}

.stats-container {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}
</style>
