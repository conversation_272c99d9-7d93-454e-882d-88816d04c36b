export const localMap = {
  /* login: require('login/index').default // 同步的方式
  login:()=> import('login/index')      // 异步的方式 */
  menu: () => import('@/views/menu/index'), // 权限管理
  role: () => import('@/views/role/index'), // 角色管理
  admin: () => import('@/views/admin/index'), // 管理员管理

  courses: () => import('@/views/course/index'), // 课程管理
  szCourses: () => import('@/views/course/szIndex'), // 深造课程列表
  courseCate: () => import('@/views/course/categoryIndex'), // 课程分类管理
  plugs: () => import('@/views/plug/index'), // 插件管理
  plugCate: () => import('@/views/plug/categoryIndex'), // 课程分类管理

  goods: () => import('@/views/shop/index'), // 周边列表
  goodCate: () => import('@/views/shop/categoryIndex'), // 周边分类
  goodsOrder: () => import('@/views/shop-orders/index'), // 周边订单
  // 新闻
  news: () => import('@/views/new/index'), // 新闻
  couponCode: () => import('@/views/coupon/index'), // 新闻
  // 配置
  private: () => import('@/views/configs/private'),//隐私协议
  service: () => import('@/views/configs/service'),//服务条款
  question: () => import('@/views/configs/question'),//常见问题
  register: () => import('@/views/configs/register'),//注册协议
  about: () => import('@/views/configs/about'),//关于我们
  contact: () => import('@/views/configs/contact'),//联系我们
  ad: () => import('@/views/configs/ad'),//广告
  copyrightStatement: () => import('@/views/configs/copyrightStatement'),//版权声明
  uploadStatement: () => import('@/views/configs/uploadStatement'),//上传声明
  downloadStatement: () => import('@/views/configs/downloadStatement'),//下载声明
  aiServer: () => import('@/views/configs/aiServer'),//Ai服务器地址

  verification: () => import('@/views/verification/index'), // 验证码管理
  user: () => import('@/views/user/index'), // 用户管理
  order: () => import('@/views/orders/index'), // 订单管理
  rechargeOrder: () => import('@/views/recharge_orders/index'), // 订单管理
  log: () => import('@/views/log/index'), // 系统日志

  aiGallery: () => import('/src/views/ai/gallery'),
  aiGalleryCategory: () => import('/src/views/ai/galleryCategory'),
  aiHot: () => import('/src/views/ai/hot'),
  aiRenderStyle: () => import('/src/views/ai/renderStyle'),
  aiNews: () => import('/src/views/ai/news'),
  aiPlug: () => import('/src/views/ai/plug'),
  aiVip: () => import('/src/views/ai/vip'),
  aiVipSetting: () => import('/src/views/ai/vipSetting'),


  //新增
  myupload: () => import('@/views/configs/myupload'), // 系统日志
  videoreview: () => import('@/views/contributecourse/index'), // 视频投稿
  chajianreview: () => import('@/views/contributeplug/index'), // 插件投稿
  sucaireview: () => import('@/views/contributeshop/index'), // 周边投稿

  tagslist: () => import('@/views/tagsList/index'), // 标签列表

}
