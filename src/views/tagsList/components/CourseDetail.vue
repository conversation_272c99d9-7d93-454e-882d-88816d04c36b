<template>
  <div>
    <el-dialog class="el-dialog-wallet" title="课程详情" top="30px" :visible.sync="visible" @closed="onClose()">
      <div class="filter-container">
        <el-form :inline="true" :model="search">
          <el-form-item label="类型">
            <el-select v-model="search.type" @clear="getList(1)" @change="getList(1)">
              <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <el-table
          v-loading="loading"
          border
          highlight-current-row
          :data="list"
      >
        <el-table-column
            prop="id"
            label="ID"
            width="80"
            align="center"
        />
        <el-table-column
            width="120"
            label="封面图"
            header-align="center"
        >
          <template slot-scope="{ row }">
            <div class="info-wrapper">
              <el-image
                  class="image-item"
                  :src="row.images && domin + row.images"
                  :preview-src-list="[domin + row.images]"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </el-image>
            </div>
          </template>
        </el-table-column>
        <el-table-column
            prop="name"
            label="标题"
            align="center"
        />
        <el-table-column
            prop="chapter_id"
            label="章节"
            align="center"
        >
          <template slot-scope="{ row }"> 
            {{ row.type!="chapter"&&row.parent&&list[row.parent-1]&&list[row.parent-1].name ? list[row.parent-1].name : '-' }}
          </template>
        </el-table-column>
        <el-table-column
            prop="type"
            label="类型"
            align="center"
        >
          <template slot-scope="{ row }">
            <div> {{ row.type | paraphrase(typeOptions) }}</div>
          </template>
        </el-table-column>
        <el-table-column
            prop="currency"
            label="课件"
            align="center"
        >
          <template slot-scope="{ row }">
            <el-popover
                placement="bottom-start"
                max-width="300"
                trigger="hover"
            >
              <div>
                <div style="max-width: 300px">{{  row.link ? domin + row.link : '-' }}</div>
              </div>
              <div slot="reference" v-if="row.link">
                地址<i class="el-icon-question" />
                <a  :href=" domin + row.link" target="_black">查看</a>
              </div>
            </el-popover>
          </template>
        </el-table-column>
      
     
        <el-table-column
            prop="created_at"
            label="添加时间"
            width="140"
            align="center"
        />
      </el-table>
    </el-dialog>
   
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import {courseDetailDelete, getDetailLists} from '@/api/course'
import AddOrUpdate from './DetailAddOrUpdate'
import { DominKey, getToken } from '@/utils/auth'
import { pickerOptions } from '@/utils/explain'

export default {
  name: 'CourseDetail',
  components: { Pagination,AddOrUpdate },
  data() {
    return {
      domin: getToken(DominKey),
      visible: false,
      downloadLoading: false,
      course: {},
      data: {
        id: 0,
        name: '',
        phone: '',
        email: '',
        avatar: ''
      },
      list: [],
      search: {
        type: '',
        start_time: '',
        end_time: '',
      },
      pages: {
        total: 0,
        limit: 20,
        current: 1
      },
      typeOptions: [
        { label: '全部', value: '' },
        { label: '任务', value: 'task' },
        { label: '章节', value: 'chapter' },
        { label: '附件', value: 'annex' },
      ],
      dateRangeValue: [],
      pickerOptions,
      loading: false,
      addOrUpdateVisible: false
    }
  },
  methods: {
    init(data) {
      this.visible = true
      if (data) {

        this.course = data
        this.getList();
      }
    },
    getList(type=null){
      var arr = [];
      for (let i = 0; i < this.course.detailList.length; i++) {
        if(this.course.detailList[i].image)this.course.detailList[i].images = this.course.detailList[i].image;
        this.course.detailList[i].link = this.course.detailList[i].fileList.length>0?this.course.detailList[i].fileList[0].name:'';
        console.log(this.course.detailList[i], 'this.course.detailList[i]--------');
        if(this.search.type){
          if(this.course.detailList[i].type==this.search.type){
            arr.push(this.course.detailList[i]);
          }
        } else {
          arr.push(this.course.detailList[i]);
        }
      }
      this.list = arr;
    },
    onClose() {
      this.$reset('data', false)
    },

  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog-wallet .el-dialog {
  width: 1240px;
}
::v-deep .user-avatar{
  vertical-align: middle;
}
</style>
