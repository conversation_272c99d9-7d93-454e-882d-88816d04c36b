<template>
  <el-dialog :title="form.id ? $t('table.edit') : $t('table.add') " :visible.sync="visible" @closed="onClose()">
    <el-form ref="form" :model="form" :rules="rules" label-width="160px">
      <el-form-item label="分类名称" prop="name">
        <el-input v-model="form.name" placeholder="分类名称" clearable />
      </el-form-item>
      <el-form-item label="上级分类" prop="cate_id">
        <el-select v-model="form.parent_id" filterable placeholder="请选择分类">
          <el-option v-for="(item, index) in cateFilterOptions" :key="index" :label="item.label" :value="item.value">
            <span>{{ item.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="排序(越大越靠前)" prop="sort">
        <el-input-number v-model="form.sort" :min="0" :precision="0" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="btnLoading" @click="onFormSubmit()">{{ $t('table.confirm') }}</el-button>
      <el-button @click="visible = false">{{ $t('table.cancel') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addOrUpdate,cateFilterList } from '@/api/category'

export default {
  name: 'CategoryAddOrUpdate',
  data() {
    return {
      visible: false,
      btnLoading: false,
      form: {
        id: 0,
        parent_id: '',
        name: '',
        sort: 0
      },

      rules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: ['blur', 'change'] }
        ],
        sort: [
          { required: true, message: '不能为空', trigger: ['blur', 'change'] }
        ]
      },

      cateFilterOptions: []
    }
  },
  methods: {
    init(data) {
      if (data && data.children_category && data.children_category.length > 0) {
        this.cateFilterOptions = []
      } else {
        this.cateFilterLists()
      }

      this.visible = true
      if (data) {
        data.parent_id = data.parent_id || ''
        this.form = Object.assign({}, data)
      }
    },
    onFormSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.btnLoading = true
          addOrUpdate(this.form)
            .then(({ msg }) => {
              this.$message.success(msg)
              this.visible = false
              this.$emit('refreshList')
            })
            .catch(() => {
              this.btnLoading = false
            })
        }
      })
    },
    onClose() {
      this.btnLoading = false
      this.$reset()
    },

    cateFilterLists() {
      cateFilterList({}).then(response => {
        this.cateFilterOptions = response.data.map(v => {
          return {
            label: v.name,
            value: v.id,
          }
        })
      })
    },
  }
}
</script>
<style scoped>
.el-input-number {
  width: 200px;
}
</style>
