<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" :model="search">
        <el-form-item label="名称">
          <el-input v-model="search.name" placeholder="名称" clearable @clear="getList(1)" @keyup.enter.native="getList(1)" />
        </el-form-item>
        <el-form-item label="电话">
          <el-input v-model="search.phone" placeholder="电话" clearable @clear="getList(1)" @keyup.enter.native="getList(1)" />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="search.email" placeholder="邮箱" clearable @clear="getList(1)" @keyup.enter.native="getList(1)" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="search.state" clearable @change="getList(1)" @clear="getList(1)">
            <el-option v-for="item in stateOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="绑定状态">
          <el-select v-model="search.bind" clearable @change="getList(1)" @clear="getList(1)">
            <el-option v-for="item in bindOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-button icon="el-icon-search" @click="getList(1)">
          {{ $t('table.search') }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" @click="onAddOrUpdate()">
          {{ $t('table.add') }}
        </el-button>
      </el-form>
    </div>

    <el-table
      v-loading="loading"
      border
      highlight-current-row
      :data="list"
    >
      <el-table-column
        prop="name"
        label="名称"
        width="180"
        align="center"
      />
      <el-table-column
        prop="phone"
        label="电话"
        align="center"
      />
      <el-table-column
        prop="email"
        label="邮箱"
        align="center"
      />
      <el-table-column
        label="角色"
        align="center"
      >
        <template slot-scope="{ row }">
          {{ (row.roles && row.roles[0] && row.roles[0].name) || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="state"
        label="状态"
        width="80"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-tag :type="row.state ? 'danger' : 'success'">{{ stateOptions[row.state + 1].label }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          label="应用端绑定"
          align="left"
      >
        <template slot-scope="{ row }">
          <span v-show="row.user_id > 0">
            <div>昵称：{{ (row.user && row.user.name) || '-' }}</div>
            <div>账号：{{ row.account || '-' }}</div>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="created_at"
        label="创建时间"
        width="160"
        align="center"
      />
      <el-table-column
        label="操作"
        width="180"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-button type="primary" @click="onAddOrUpdate(row)">编辑</el-button>
          <el-button type="danger" @click="onDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="pages.total > 0" :total="pages.total" :page.sync="pages.current" :limit.sync="pages.limit" @pagination="getList()" />

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshList="getList()"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { dataList, deleteData } from '@/api/admin'
import AddOrUpdate from './components/AddOrUpdate'

export default {
  name: 'Admin',
  components: { Pagination, AddOrUpdate },
  data() {
    return {
      list: [],
      search: {
        name: '',
        phone: '',
        email: '',
        state: '',
        bind: 0,
      },
      pages: {
        total: 0,
        limit: 20,
        current: 1
      },
      stateOptions: [
        { label: '全部', value: '' },
        { label: '正常', value: 0 },
        { label: '禁用', value: 1 }
      ],
      bindOptions: [
        { label: '全部', value: '-1' },
        { label: '未绑定', value: 0 },
        { label: '已绑定', value: 1 }
      ],
      loading: false,
      addOrUpdateVisible: false
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.getList()
    },
    getList(page = this.pages.current, loading = true) {
      if (this.loading) return
      this.loading = loading
      if (page === 1) this.pages.current = page
      dataList({ page, ...this.search, limit: this.pages.limit })
        .then(response => {
          if (response.code !== 200) return
          this.list = response.data.data
          this.pages.total = response.data.total
        })
        .catch((error) => {
          this.$message.error(error.msg)
        })
        .finally(() => {
          this.loading = false
        })
    },
    onAddOrUpdate(data) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate && this.$refs.addOrUpdate.init(data)
      })
    },
    onDelete({ name, id }) {
      this.$confirm(
        `确定对[${name}(#${id})]进行[删除]操作?`,
        '删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'error',
          cancelButtonClass: 'btn-custom-cancel'
        }
      )
        .then(() => {
          deleteData(id)
            .then(({ msg = '删除成功' }) => {
              this.$message.success(msg)
              this.init()
            })
            .catch(() => {
              this.$message.error('删除失败')
            })
        })
        .catch(() => {})
    }
  }
}
</script>
