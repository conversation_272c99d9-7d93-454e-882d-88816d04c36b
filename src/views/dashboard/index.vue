<template>
  <div class="dashboard-container">
    <div class="app-container">
      <history-item :statistics="data" v-if="accountBind<=0"/>
      <div v-else>欢迎使用radirhino管理后台</div>
    </div>
  </div>
</template>

<script>
import HistoryItem from './components/HistoryItem'
import { getHome } from '@/api/common'
import { BindKey, getToken } from '@/utils/auth'
import 'driver.js/dist/driver.min.css' // import driver.js css

export default {
  name: 'Dashboard',
  components: {
    HistoryItem,
  },
  data() {
    return {
      driver: null,
      accountBind: getToken(BindKey),
      data: {
        user: {
          total: 0,
          today: 0,
        },
        order_count: {
          total: 0,
          today: 0,
        },
        order_amount: {
          total: 0,
          today: 0,
        }
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.getList()
    },
    getList() {
      getHome()
        .then(response => {
          this.data = response.data
        })
    },
  }
}
</script>
<style lang="scss" scoped>
.dashboard-container {
  min-height: inherit;
  background-color: #F0F2F5;
}
</style>
