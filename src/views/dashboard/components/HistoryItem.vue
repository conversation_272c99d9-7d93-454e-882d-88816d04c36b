<template>
  <el-row :gutter="20" class="panel-box">
    <el-col :xs="24" :span="12" class="card-panel-col">
      <el-row :gutter="20" class="panel-group">
        <el-col :xs="24" :span="12">
          <el-card shadow="never" class="card-boder">
            <div class="user-title">
              今日新增用户
            </div>
            <div class="user-num">
              <b>{{ statistics.user.today }}</b>
            </div>
            <div class="user-text" />
            <el-divider />
            <div class="user-title">
              累计用户
            </div>
            <div class="user-num">
              <b>{{ statistics.user.total }}</b>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :span="12">
          <el-card shadow="never" class="card-boder">
            <div class="user-title">
              今日下单光子金额
            </div>
            <div class="user-num">
              <span>{{ statistics.order_amount.today | moneyToFormat }}</span>
            </div>
            <div class="user-text">
              累计下单光子金额 <span> {{ statistics.order_amount.total | moneyToFormat }}</span>
            </div>
            <el-divider />
            <div class="user-title">
              今日新增订单
            </div>
            <div class="user-num">
              <span>{{ statistics.order_count.today }}</span>
            </div>
            <div class="user-text">
              累计订单 <span> {{ statistics.order_count.total }}</span>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-col>
  </el-row>
</template>

<script>
export default {
  props: {
    statistics: {
      type: Object,
      default: () => ({
        user: {
          total: 0,
          today: 0,
        },
        order_count: {
          total: 0,
          today: 0,
        },
        order_amount: {
          total: 0,
          today: 0,
        }
      })
    }
  },
  data() {
    return {
    }
  },
  created() {
  },
  methods: {
    init() {
    }
  }
}
</script>

<style lang="scss" scoped>
.card-panel-col {
  margin-bottom: 20px;
}
.card-boder {
  border: 0px;
  min-height: 280px!important;
  .el-divider {
    margin: 20px 0;
  }
  .user-title {
    font-size: 14px;
  }
  .user-num {
    font-size: 30px;
    margin: 16px 0;
    span {
      color: #e6a23c;
    }
    b {
      color: #f56c6c;
      font-weight: normal;
    }
  }
  .user-text {
    font-size: 14px;
    height: 16px;
    color: #8C8C8C;
    b {
      color: #f56c6c;
      font-weight: normal;
    }
    span {
      color: #000;
    }
  }
}

@media (min-width: 768px) {
}
</style>
