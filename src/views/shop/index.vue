<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" :model="search">
        <el-form-item label="名称">
          <el-input
            v-model="search.name"
            placeholder="名称"
            clearable
            @clear="getList(1)"
            @keyup.enter.native="getList(1)"
          />
        </el-form-item>
        <el-form-item label="上架状态">
          <el-select
            v-model="search.status"
            clearable
            placeholder="上架状态"
            @change="getList(1)"
          >
            <el-option label="全部" value="" />
            <el-option label="否" :value="1" />
            <el-option label="是" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类筛选">
          <el-select v-model="search.category_id" clearable placeholder="请选择分类" @change="getList(1)">
            <el-option v-for="item in categoryOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-button icon="el-icon-search" @click="getList(1)">
          {{ $t("table.search") }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" @click="onAddOrUpdate()">
          {{ $t("table.add") }}
        </el-button>
        <el-button type="warning" icon="el-icon-discount" @click="onDiscountConfig()">
          打折配置
        </el-button>
      </el-form>
    </div>
    <el-table v-loading="loading" border highlight-current-row :data="list">
      <el-table-column prop="id" label="ID" width="80" align="center" />
      <el-table-column
        prop="images"
        label="商品图片"
        width="280"
        header-align="center"
      >
        <template slot-scope="{ row, $index }">
          <div v-if="!loading" class="recommend-page">
            <swiper :ref="'mySwiper' + $index" :options="swiperOption">
              <swiper-slide
                v-for="(item, index) in row.images"
                :key="index"
                class="images-list"
              >
                <el-image
                  class="image-item"
                  fit="contain"
                  :src="item && domin + item"
                  @click="onPicturePreview(row.images, index)"
                />
              </swiper-slide>
            </swiper>
            <div
              v-if="row.images.length > 3"
              slot="button-prev"
              class="swiper-button-prev"
              @click="prev($index)"
            />
            <div
              v-if="row.images.length > 3"
              slot="button-next"
              class="swiper-button-next"
              @click="next($index)"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        label="商品名称"
        width="150"
        header-align="center"
      />

      <el-table-column min-width="140" label="标签" header-align="center">
        <template slot-scope="{ row }">
          <el-tag v-for="(item, i) in row.tag_list_info" :key="i">
            {{ item.tag }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="推荐人" width="80" header-align="center">
        <template slot-scope="{ row }">
          {{ row.suggest_user ? row.suggest_user.name : "平台" }}
        </template>
      </el-table-column>
      <el-table-column label="商品信息" min-width="150" header-align="center">
        <template slot-scope="{ row }">
          <div>
            分类：
            {{
              row.cate
                ? row.cate.parent_category
                  ? row.cate.parent_category.name + " / " + row.cate.name
                  : row.cate.name
                : "-"
            }}
          </div>
          <div>价格(光子)：{{ row.price || 0 | moneyToFormat }}</div>
          <div>划线价(光子)：{{ row.market_price || 0 | moneyToFormat }}</div>
          <!--          <div>剩余库存：{{ row.stock || 0 }}</div>-->
          <div>浏览量：{{ row.sales_num || 0 }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        label="其他设置"
        min-width="100"
        header-align="center"
      >
        <template slot-scope="{ row }">
          <div>邮费：{{ row.postage || 0 | moneyToFormat }}</div>
          <!--          <div>光粒抵扣：{{ row.is_integral?'是':'否' }}</div>-->
          <!--          <div v-show="row.is_integral">-->
          <!--            光粒抵扣数量：{{ row.integral_num>0?row.integral_num:'不限' }}-->
          <!--          </div>-->
          <!--          <div>光粒赠送：{{ row.give_integral||0 }}</div>-->
        </template>
      </el-table-column>
      <el-table-column width="70" label="上架状态" align="center">
        <template slot-scope="{ row, $index }">
          <el-switch
            v-model="row.status"
            :active-value="1"
            :inactive-value="0"
            @change="onChangeStatus(row, $index)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="sort" width="70" label="排序" align="center" />
      <el-table-column label="操作" width="200" fixed="right" align="center">
        <template slot-scope="{ row, $index }">
          <el-button-group>
            <el-button type="primary" plain @click="onComment(row)"
              >评论</el-button
            >
            <el-button type="primary" @click="onAddOrUpdate(row)"
              >编辑</el-button
            >
            <el-button type="danger" @click="onDelete({ row, $index })"
              >删除</el-button
            >
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="pages.total > 0"
      :total="pages.total"
      :page.sync="pages.current"
      :limit.sync="pages.limit"
      @pagination="getList()"
    />

    <el-image-viewer
      v-if="imageViewer"
      :on-close="onCloseViewer"
      :url-list="imageViewerList"
    />

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshList="getList()"
    />
    <!-- 弹窗, 评论 -->
    <comment v-if="commentVisible" ref="comment" />
    <!-- 打折配置弹窗 -->
    <discount-config
      v-if="discountConfigVisible"
      ref="discountConfig"
      @refreshList="getList()"
    />
  </div>
</template>

<script>
import Pagination from "@/components/Pagination";
import AddOrUpdate from "./components/AddOrUpdate";
import { dataList, deleteData, setStatus, cateList } from "@/api/shop";
import { DominKey, getToken } from "@/utils/auth";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
import "swiper/swiper-bundle.css";
import Comment from "../comment/Comment";
import DiscountConfig from '@/components/DiscountConfig'

export default {
  name: "Shop",
  components: {
    Comment,
    Pagination,
    AddOrUpdate,
    ElImageViewer,
    Swiper,
    SwiperSlide,
    DiscountConfig,
  },
  data() {
    return {
      domin: getToken(DominKey),
      swiperOption: {
        slidesPerView: 3,
        spaceBetween: 10,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
      },
      list: [],
      search: {
        name: "",
        status: "",
        category_id: "",
      },
      pages: {
        total: 0,
        limit: 20,
        current: 1,
      },
      imageViewer: false,
      imageViewerList: [],
      loading: false,
      addOrUpdateVisible: false,
      commentVisible: false,
      discountConfigVisible: false,
      categoryOptions: [],
    };
  },
  computed: {
    swiper() {
      return function (v = 0) {
        return this.$refs[`mySwiper${v}`].$swiper;
      };
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getCategoryList();
      this.getList();
    },
    getCategoryList() {
      cateList()
        .then((response) => {
          if (response.code === 200) {
            this.categoryOptions = response.data || [];
          }
        })
        .catch(() => {
          // 错误处理
        });
    },
    getList(page = this.pages.current, loading = true) {
      if (this.loading) return;
      this.loading = loading;
      if (page === 1) this.pages.current = page;
      dataList({ page, ...this.search, limit: this.pages.limit })
        .then((response) => {
          this.list = response.data.data;
          this.pages.total = response.data.total;
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },
    onAddOrUpdate(data) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate && this.$refs.addOrUpdate.init(data);
      });
    },
    onChangeStatus(row, index) {
      setStatus({ id: row.id, status: row.status })
        .then(({ msg = "设置成功" }) => {
          this.$message.success(msg);
        })
        .catch((msg = "设置失败") => {
          row.status = row.status ? 0 : 1;
          this.$message.error(msg);
        });
    },
    onComment(data) {
      this.commentVisible = true;
      data.target_type = "goods";
      this.$nextTick(() => {
        this.$refs.comment && this.$refs.comment.init(data);
      });
    },
    onDelete({ row, $index }) {
      this.$confirm(
        `确定对[(#${row.id})(${row.name})]进行[删除]操作?`,
        "删除",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "error",
          cancelButtonClass: "btn-custom-cancel",
        }
      )
        .then(() => {
          deleteData(row.id)
            .then(({ msg = "删除成功" }) => {
              this.$message.success(msg);
              this.init();
            })
            .catch(() => {});
        })
        .catch(() => {});
    },
    onPicturePreview(imgArr, index) {
      const newImgArr = imgArr.concat();
      const currentItemArr = newImgArr.slice(index, index + 1);
      newImgArr.splice(index, 1);
      const currentArr = currentItemArr.concat(newImgArr).map((v) => {
        return this.domin + v;
      });
      this.imageViewerList = currentArr;
      this.imageViewer = true;
    },
    onCloseViewer() {
      this.imageViewer = false;
    },
    prev(index) {
      this.swiper(index).slidePrev();
    },
    next(index) {
      this.swiper(index).slideNext();
    },
    onDiscountConfig() {
      this.discountConfigVisible = true
      this.$nextTick(() => {
        this.$refs.discountConfig && this.$refs.discountConfig.init()
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.images-list {
  width: 80px;
  height: 80px;
}
.images-list .image-item {
  height: 100%;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
}
::v-deep .images-list .image-item img {
  height: auto;
}
.recommend-page {
  width: 291px;
  margin: 0 auto;
  .swiper-container {
    user-select: none;
  }
  .swiper-button-prev,
  .swiper-button-next {
    display: none;
    pointer-events: auto;
    cursor: pointer;
  }
  .swiper-button-prev::after,
  .swiper-button-next::after {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }
  &:hover .swiper-button-prev,
  &:hover .swiper-button-next {
    display: flex;
  }
  .images-list {
    width: 80px;
    height: 80px;
    .image-item {
      height: 100%;
      cursor: pointer;
      display: -webkit-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      -webkit-justify-content: center;
      justify-content: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      -webkit-align-items: center;
      align-items: center;
      img {
        height: auto;
      }
    }
  }
}
.el-link {
  vertical-align: baseline;
  cursor: text;
  font-size: 12px;
}
</style>



