<template>
  <el-dialog top="30px" width="1000px" :title="form.id ? $t('table.edit') : $t('table.add') " :visible.sync="visible" :close-on-click-modal="false" :close-on-press-escape="false" @closed="onClose()">
    <el-form ref="form" :model="form" :rules="rules" label-width="160px">
      <template>
        <el-form-item label="封面图" prop="images">
          <custom-upload
              class-name="avatar-uploader"
              ref-name="images"
              @handleBeforeUpload="beforeAvatarUpload"
              @handleSuccess="handleAvatarSuccess"
          >
            <img v-if="form.images" :src="domin + form.images" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </custom-upload>
          <div class="notice">
            注意：建议封面图尺寸 750*750px
          </div>
        </el-form-item>
      </template>
      <el-form-item label="标题名称" prop="name">
        <el-input v-model="form.name" placeholder="标题名称" />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="form.type" filterable placeholder="请选择类型">
          <el-option
              v-for="(item, index) in typeOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
          >
            <span>{{ item.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.type !== 'chapter'" label="章节" prop="chapter_id">
        <el-select v-model="form.chapter_id" filterable placeholder="请选择章节">
          <el-option
              v-for="(item, index) in chapterOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
          >
            <span>{{ item.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否锁定" prop="lock_status" v-show="form.type !== 'annex'">
        <el-switch
            v-model="form.lock_status"
            :inactive-value="0"
            :active-value="1"
        />
      </el-form-item>
      <el-form-item v-if="form.type !== 'chapter' && form.lock_status !== 1" label="课件" prop="link">
        <custom-upload
            class-name=""
            :show-file-list="true"
            :limit="1"
            @handleBeforeUpload="beforeFileUpload"
            @handleSuccess="handleFileSuccess"
            @handleExceed="handleExceed"
            @elProgress="elProgress"
        >
          <el-button type="primary">点击上传</el-button>
        </custom-upload>
        <el-progress v-if="![0, 1].includes(percentage)" :percentage="percentage * 100" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="form.sort" :precision="4" :min="0" placeholder="排序" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="btnLoading" @click="onFormSubmit()">
        {{ $t('table.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('table.cancel') }}
      </el-button>
    </div>

    <el-image-viewer
        v-if="imageViewer"
        :z-index="3000"
        :on-close="closeViewer"
        :url-list="imageViewerList"
    />
  </el-dialog>
</template>

<script>
import CustomUpload from '@/components/Upload/CustomUpload'
import {addOrUpdate, chapterList, detailAddOrUpdate, taskDetails} from '@/api/course'
import { DominKey, getToken } from '@/utils/auth'
import draggable from 'vuedraggable'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
export default {
  name: 'DetailAddOrUpdate',
  components: { CustomUpload, ElImageViewer, draggable },
  data() {
    return {
      percentage: 0,
      visible: false,
      course_id: 0,
      btnLoading: false,
      editTinymceVisible: false,
      imageViewer: false,
      imageViewerList: [],
      domin: getToken(DominKey),
      currentName: '',
      cateOptions:[],
      typeOptions: [
        { label: '任务', value: 'task' },
        { label: '章节', value: 'chapter' },
        { label: '附件', value: 'annex' },
      ],
      // 章节
      chapterOptions:[],
      form: {
        name: '',
        images: '',
        link: '',
        lock_status: 0,
        chapter_id: 0,
        sort: 0,
      },
      rules: {
        name: [
          { required: true, message: '请输入标题名称', trigger: ['blur', 'change'] }
        ],
        type: [
          { required: true, message: '请选择类型', trigger: ['blur', 'change'] }
        ],
      }
    }
  },
  computed: {
    dragOptions() {
      return {
        animation: 200,
        group: 'description',
        disabled: false,
        ghostClass: 'ghost'
      }
    }
  },
  methods: {
    init(data) {
      this.visible = true
      if (data) {
        if (data.course_id && data.course_id > 0) {
          this.form.id = data.id
          this.course_id = data.course_id;
          this.getDetails()
          this.chapterLists(data.course_id)
        } else {
          this.chapterLists(data.id)
          this.course_id = data.id;
        }
      }
    },
    getDetails() {
      taskDetails(this.form.id)
          .then(response => {
            this.form = response.data
          })
          .catch(() => {})
    },
    onFormSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id) {
            this.$confirm(`确定对[(#${this.form.id})]进行修改?`, '编辑', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
                .then(() => {
                  this.hanldaddOrUpdate()
                })
                .catch(() => {
                })
          } else {
            this.hanldaddOrUpdate()
          }
        }
      })
    },
    hanldaddOrUpdate() {
      this.form.course_id = this.course_id
      const data = JSON.parse(JSON.stringify(this.form))
      this.btnLoading = true
      detailAddOrUpdate(data)
          .then(({ msg }) => {
            this.$message.success(msg)
            this.visible = false
            this.$emit('refreshList')
          })
          .catch(() => {
            this.btnLoading = false
          })
    },
    onClose() {
      this.$reset()
    },
    handleRemove(file, fileList) {
      this[`${file.name.split('.')[1]}List`] = []
    },
    closeViewer() {
      this.imageViewer = false
    },
    // 上传文件
    handleAvatarSuccess(response, file) {
      this.form[this.currentName] = response.name
    },
    beforeAvatarUpload(file, cb, refName) {
      const type = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
      const isLt2M = file.size / 1024 / 1024 < 20
      if (!type.includes(file.type)) {
        this.$message.error('上传图片只能是 ' + type.join(',') + ' 格式!')
        cb(false)
        return
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 20M')
        cb(false)
        return
      }
      this.currentName = refName
      cb(true)
    },
    // 上传文件
    handleFileSuccess(response, file) {
      this.form.link = response.name
    },
    beforeFileUpload(file, cb, refName) {
      const isLt2M = file.size / 1024 / 1024 < 2048
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 2048M')
        cb(false)
        return
      }
      this.currentName = refName
      cb(true)
    },
    elProgress(p, cpt, res) {
      this.percentage = p
    },
    // 章节
    chapterLists(course_id) {
      chapterList(course_id).then(response => {
        this.chapterOptions = response.data.map(v => {
          return {
            label: v.name,
            value: v.id,
          }
        })
      })
    },
    handleError() {
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件，请删除后在上传`)
    }
  }
}
</script>
<style lang="scss" scoped>
.el-input-number {
  width: 200px;
}
.box-card {
  margin-bottom: 18px;
}
.avatar-uploader {
  display: inline-block;
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
    object-fit: contain;
  }
}
::v-deep .filter-list-box {
  .wrapper {
    display: inline;
    vertical-align: top;
  }
  .upload-images {
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #c0ccda;
    border-radius: 6px;
    box-sizing: border-box;
    width: 100px;
    height: 100px;
    margin: 0 8px 8px 0;
    display: inline-block;
    position: relative;
    cursor: grabbing;
    .upload-image {
      width: 100%;
      height: 100%;
      display: -webkit-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      -webkit-justify-content: center;
      justify-content: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      -webkit-align-items: center;
      align-items: center;
      & > img {
        width: 100%;
      }
    }
    .upload-actions {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background-color: rgba(0,0,0,0.5);
      text-align: center;
      display: none;
      i {
        margin-left: 6px;
        margin-top: 6px;
        &:first-child {
          margin-left: 0;
        }
      }
    }
    &:hover .upload-actions {
      display: block;
    }
    .upload-actions i {
      color: #fff;
      font-size: 18px;
      cursor: pointer;
    }
  }
}
.notice {
  margin: 10px 0 0 0;
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
}
</style>
