<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" :model="search">
        <el-form-item label="手机号">
          <el-input
            v-model="search.phone"
            placeholder="手机号"
            clearable
            @clear="getList(1)"
            @keyup.enter.native="getList(1)"
          />
        </el-form-item>
        <el-form-item label="订单号">
          <el-input
            v-model="search.order_no"
            style="width: 220px"
            placeholder="订单号"
            clearable
            @clear="getList(1)"
            @keyup.enter.native="getList(1)"
          />
        </el-form-item>
        <el-form-item label="支付类型">
          <el-select
            v-model="search.pay_method"
            clearable
            @change="getList(1)"
            @clear="getList(1)"
          >
            <el-option
              v-for="item in payMethodOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="产品类型">
          <el-select
            v-model="search.product_type"
            clearable
            @change="getList(1)"
          >
            <el-option
              v-for="item in productTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="下单时间">
          <el-date-picker
            v-model="dateRangeValue"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
            @change="onChangeDateRange"
          />
        </el-form-item>
        <el-form-item label="状态" style="margin-left: 10px">
          <el-radio-group
            v-model="search.status"
            size="mini"
            @change="onChangeStatus"
          >
            <el-badge
              v-for="item in statusOptions"
              :key="item.value"
              class="item"
            >
              <el-radio-button :label="item.value">
                {{ item.label }}
              </el-radio-button>
            </el-badge>
          </el-radio-group>
        </el-form-item>
        <el-button icon="el-icon-search" @click="getList(1)">
          {{ $t('table.search') }}
        </el-button>
        <el-button
          :loading="downloadLoading"
          type="success"
          icon="el-icon-document"
          @click="onHandleDownload"
        >
          {{ $t('table.export') }} Excel
        </el-button>
      </el-form>
    </div>

    <el-table
        v-loading="loading"
        border
        highlight-current-row
        :data="list"
    >
      <el-table-column
          prop="id"
          label="ID"
          width="55"
          align="center"
      />
      <el-table-column width="200" header-align="center" label="用户">
        <template slot-scope="{ row }">
          <div v-if="row.user">
            <el-avatar
                :key="row.user.id"
                icon="el-icon-user-solid"
                :src="row.user.avatar"
            />
            <div style="display: inline-block; margin-left: 2px">
              <div>
                {{ row.user.name }}
              </div>
              <div>
                {{ row.user.phone || row.user.email }}
              </div>
            </div>
          </div>
          <div v-else>
            -----
          </div>
        </template>
      </el-table-column>

      <el-table-column
          prop="order_no"
          label="订单号"
          width="200"
          align="center"
      />
      <el-table-column
          min-width="170"
          label="产品名称"
          header-align="center"
      >
        <template slot-scope="{ row }">
          {{ row.product_name }}
        </template>
      </el-table-column>
      <el-table-column
          width="130"
          label="产品类型"
          align="center"
      >
        <template slot-scope="{ row }">
          {{ row.product_type | paraphrase(productTypeOptions) }}
        </template>
      </el-table-column>
      <el-table-column
          prop="order_amount"
          width="120"
          label="支付价格"
          align="center"
      />
      <el-table-column
          width="130"
          label="支付类型"
          align="center"
      >
        <template slot-scope="{ row }">
          {{ row.pay_method | paraphrase(payMethodOptions) }}
        </template>
      </el-table-column>
      <el-table-column
          width="130"
          label="状态"
          align="center"
      >
        <template slot-scope="{ row }">
          <el-tag v-if="row.status === 1" type="success">已支付</el-tag>
          <el-tag v-else-if="row.status === 0"type="warning">待支付</el-tag>
          <el-tag v-else-if="row.status === 2"type="info">已关闭</el-tag>
        </template>
      </el-table-column>
      <el-table-column
          width="140"
          prop="created_at"
          label="下单时间"
          header-align="center"
      />
      <el-table-column
          width="140"
          prop="pay_time"
          label="支付时间"
          header-align="center"
      />
    </el-table>
    <pagination
      v-show="pages.total > 0"
      :total="pages.total"
      :page.sync="pages.current"
      :limit.sync="pages.limit"
      @pagination="getList()"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getToken, DominKey } from '@/utils/auth'
import {
  dataList,
  exportOrder,
} from '@/api/order'
import {pickerOptions } from '@/utils/explain'
import { mapGetters } from 'vuex'

export default {
  name: 'Orders',
  components: { Pagination },
  data() {
    return {
      domin: getToken(DominKey),
      dateRangeValue: [],
      pickerOptions,
      payMethodOptions:[
        { label: '全部', value: '' },
        { label: '无', value: 0 },
        { label: '微信', value: 1 },
        { label: '支付宝', value: 2 }
      ],
      productTypeOptions:[
        { label: '全部', value: '' },
        { label: '课程', value: 1, type: 'warning' },
        { label: '插件', value: 2, type: 'success' },
        { label: '周边', value: 3, type: 'info' }
      ],
      statusOptions:[
        { label: '全部', value: '' },
        { label: '待支付', value: 0, type: 'warning' },
        { label: '已支付', value: 1, type: 'success' },
        { label: '已关闭', value: 2, type: 'info' }
      ],
      list: [],
      wait_count: 0,
      search: {
        phone: '',
        order_no: '',
        status: '',
        pay_method: '',
        start_time: '',
        end_time: '',
        product_type: ''
      },
      pages: {
        total: 0,
        limit: 20,
        current: 1
      },
      orders: [],
      downloadLoading: false,
      countVisible: false,
      loading: false,
      btnLoading: false
    }
  },

  computed: {
    ...mapGetters(['info'])
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.search.status = this.$route.query.status === undefined ? '' : this.$route.query.status
      this.getList()
    },
    getList(page = this.pages.current, loading = true) {
      this.list = []
      if (this.loading) return
      this.loading = loading
      if (page === 1) this.pages.current = page
      dataList({ page, ...this.search, limit: this.pages.limit })
        .then((response) => {
          if (response.code !== 200) return
          this.list = response.data.data.data.map((v) => {
            return Object.assign(v, {
              isClose: +new Date() > +new Date(v.created_at) + 1800 * 1000
            })
          })
          this.pages.total = response.data.data.total
          this.wait_count = response.data.wait_count
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },

    onChangeDateRange(value) {
      if (Array.isArray(value)) {
        this.search.start_time = value[0]
        this.search.end_time = value[1]
      } else {
        this.search.start_time = this.search.end_time = ''
        this.getList(1)
      }
    },
    onHandleDownload() {
      this.downloadLoading = true
      exportOrder(this.search)
        .then((data) => {
          location.href = '/' + data.data.filename
        })
        .catch(() => {})
        .finally(() => {
          this.downloadLoading = false
        })
    },
    onChangeStatus(value) {
      this.getList(1)
    },

    onHandleCount(row) {
      this.countVisible = true
      this.$nextTick(() => {
        this.$refs.orderCount && this.$refs.orderCount.init(row)
      })
    },

    headNone({ row, colunm, rowIndex, columnIndex }) {
      if (rowIndex > 0) {
        return { display: 'none' }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.info-wrapper {
  display: flex;
  .image-item {
    flex-shrink: 0;
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    img {
      height: auto;
    }
    ::v-deep .image-slot {
      font-size: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background: #f5f7fa;
      color: #909399;
    }
  }
}

.chain {
  display: flex;
  .chain-title {
    flex-shrink: 0;
  }
}

::v-deep .el-table th {
  user-select: auto;
}
.order-table {
  margin-top: 20px;
}
.order-table .el-button + .el-button {
  margin-left: 0;
}
::v-deep .grid-content {
  color: #606266;
  font-size: 14px;
}
::v-deep .item .el-badge__content {
  transform: translateY(-50%) translateX(50%);
  z-index: 1;
}
::v-deep .el-radio-button:first-child .el-radio-button__inner {
  border-radius: 0;
  border-left: 0;
}
::v-deep
  .item:first-child
  .el-radio-button:first-child
  .el-radio-button__inner {
  border-left: 1px solid #dcdfe6;
  border-radius: 4px 0 0 4px;
}
::v-deep .item:last-child .el-radio-button:first-child .el-radio-button__inner {
  border-radius: 0 4px 4px 0;
}
</style>
