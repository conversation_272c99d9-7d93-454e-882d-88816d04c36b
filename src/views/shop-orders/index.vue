<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" :model="search">
        <el-form-item label="订单号">
          <el-input
            v-model="search.order_no"
            placeholder="请输入订单号"
            clearable
            @clear="getList(1)"
            @keyup.enter.native="getList(1)"
          />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input
            v-model="search.phone"
            placeholder="请输入账号"
            clearable
            @clear="getList(1)"
            @keyup.enter.native="getList(1)"
          />
        </el-form-item>
        <el-form-item label="支付类型">
          <el-select
              v-model="search.pay_method"
              clearable
              @change="getList(1)"
              @clear="getList(1)"
          >
            <el-option
                v-for="item in payMethodOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="产品类型">
          <el-select
              v-model="search.product_type"
              clearable
              @change="getList(1)"
          >
            <el-option
                v-for="item in productTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="下单时间">
          <el-date-picker
            v-model="dateRangeValue"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="onChangeDateRange"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group
            v-model="search.status"
            size="small"
            @change="onChangeStatus"
          >
            <el-badge
              v-for="item in statusOptions"
              :key="item.value"
              class="item"
            >
              <el-radio-button :label="item.value">{{
                item.label
              }}</el-radio-button>
            </el-badge>
          </el-radio-group>
        </el-form-item>
        <el-button icon="el-icon-search" @click="getList(1)">
          {{ $t('table.search') }}
        </el-button>
        <el-button
          :loading="downloadLoading"
          type="success"
          icon="el-icon-document"
          @click="onHandleDownload"
        >
          导出 Excel
        </el-button>
      </el-form>
    </div>
    <el-table
      border
      :show-header="false"
      :cell-style="{ background: '#DCDFE6' }"
      :data="[
        {
          shopInfo: '订单信息',
          handle: '操作'
        }
      ]"
    >
      <el-table-column prop="shopInfo" label="订单信息" min-width="1100" />
      <el-table-column
        prop="handle"
        label="操作"
        width="180"
        fixed="right"
        align="center"
      />
    </el-table>
    <el-empty v-if="list.length === 0" v-loading="loading" description="暂无数据" />
    <el-table
      v-for="item in list"
      :key="JSON.stringify({ id: item.id })"
      class="order-table"
      :data="[item]"
      :cell-style="{ background: '#fff' }"
      :header-cell-style="headNone"
    >
      <el-table-column>
        <template slot="header">
          <span># {{ item.id }}</span>
          <el-divider direction="vertical" />
          <span>订单号: {{ item ? item.order_no : '-' }}</span>
          <el-divider direction="vertical" />
          <span>下单时间：{{ item.created_at }}</span>
          <el-divider direction="vertical" />
          <span>
            订单状态：
            <el-link
              style="vertical-align: baseline;"
              :underline="false"
              :type="$options.filters.paraphrase(item.status,statusOptions,'value','type')"
            >
              {{ $options.filters.paraphrase(item.status,statusOptions) }}
            </el-link>
          </span>
          <el-divider direction="vertical" />
          <span v-if="item.status==1">
            发货状态：
            <el-link
                style="vertical-align: baseline;"
                :underline="false"
                :type="item.logistic_status===1?'success':'info'"
            >
              {{ item.logistic_status===1?'已发货':'未发货' }}
            </el-link>
          </span>
        </template>
        <el-table-column width="300" header-align="center">
          <template slot-scope="{ row }">
            <div class="info-wrapper">
              <el-image
                class="image-item"
                :src="`${domin}${row.product_img}`"
                :preview-src-list="[`${domin}${row.product_img}`]"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </el-image>
              <div style="margin-left: 10px">
                <p>产品名称：{{ row.product_name }}</p>
                <p>产品类型：<span style="color: blue">{{ row.product_type | paraphrase(productTypeOptions) }}</span></p>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column min-width="170" header-align="center">
          <template slot-scope="{ row }">
            <div v-if="row.user">
              <el-avatar
                :key="row.user.id"
                icon="el-icon-user-solid"
                :src="row.user.avatar || ''"
              />
              <div style="display: inline-block; margin-left: 2px">
                <div>
                  {{ row.user.name }}
                </div>
                <div>
                  {{ row.user.phone}}
                </div>
              </div>
            </div>
            <div v-else>
              --
            </div>
          </template>
        </el-table-column>
        <el-table-column min-width="170">
          <template slot-scope="{ row }">
            <div>价格：{{ row.order_amount || 0 | moneyToFormat }} 光子</div>
            <div>购买量：{{ row.num }}</div>
            <div>
              <span>支付方式：{{ row.pay_method | paraphrase(payMethodOptions) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column min-width="170">
          <template slot-scope="{ row }">
<!--            <div>光粒抵扣金额：{{ row.integral_deduct_price }}</div>-->
            <div>运费金额：{{ row.postage }}</div>
            <div>优惠码：{{ row.coupon_code || '-' }}</div>
            <div>优惠金额：{{ row.deduct_price }}</div>
          </template>
        </el-table-column>
        <el-table-column min-width="170">
          <template slot-scope="{row}">
            <div>收货人：{{ row.consignee ? row.consignee.name : "---" }}</div>
            <div>手机号：{{ row.consignee ? row.consignee.phone : "---" }}</div>
            <div class="address">地址：{{ row.consignee ? row.consignee.address : "---" }}</div>
            <el-popover
              placement="bottom-start"
              max-width="300"
              trigger="hover"
            >
              <div v-if="row.logistic">
                <div>物流公司：{{ row.logistic.logistics_name }}</div>
                <div>快递单号：{{ row.logistic.logistics_no }}</div>
              </div>
              <div v-if="row.status===1 && row.logistic" slot="reference">
                物流<i class="el-icon-question" />
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column width="200">
          <template slot-scope="{ row }">
            <div>付款时间：{{ row.pay_time || '---' }}</div>
            <div>
              发货时间：{{ row.logistic ? row.logistic.delivery_time : '---' }}
            </div>
            <div>
              收货时间：{{ row.logistic ? row.logistic.receipt_time : '---' }}
            </div>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column align="center" width="180" fixed="right">
        <template slot-scope="{ row }">
          <div></div>
          <el-button v-show="row.status===1&&row.logistic_status===0 && row.consignee" type="primary" @click="handleDelivery(row)">去发货</el-button>
          <el-button v-show="row.status===1&&row.logistic_status===1" type="primary" plain @click="onLogistics(row)">查看物流</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="pages.total > 0"
      :total="pages.total"
      :page.sync="pages.current"
      :limit.sync="pages.limit"
      @pagination="getList()"
    />
    <!-- 物流弹窗 -->
    <delivery-dialog
      v-if="deliveryDialogVisible"
      ref="deliveryDialog"
      @refreshList="getList()"
    />
  </div>
</template>

<script>
import {
  pickerOptions
} from '@/utils/explain'
import { dataList, exportExcel } from '@/api/shop-management'
import { getToken, DominKey } from '@/utils/auth'
import DeliveryDialog from './component/DeliveryDialog'
import Pagination from '@/components/Pagination'
export default {
  name: 'GoodsOrder',
  components: { DeliveryDialog, Pagination },
  data() {
    return {
      domin: getToken(DominKey),
      search: {
        status: '',
        order_no: '',
        start_time: '',
        end_time: '',
        phone: '',
      },
      dateRangeValue: [],
      pickerOptions,
      logisticStatusOptions:[
        { label: '全部', value: '' },
        { label: '待发货', value: 0, type: 'warning' },
        { label: '已发货', value: 1, type: 'primary' },
      ],
      payMethodOptions:[
        { label: '全部', value: '' },
        { label: '无', value: 0 },
        { label: '微信', value: 1 },
        { label: '支付宝', value: 2 }
      ],
      productTypeOptions:[
        { label: '全部', value: '' },
        { label: '课程', value: 1, type: 'warning' },
        { label: '插件', value: 2, type: 'success' },
        { label: '周边', value: 3, type: 'info' }
      ],
      statusOptions:[
        { label: '全部', value: '' },
        { label: '待支付', value: 0, type: 'warning' },
        { label: '已支付', value: 1, type: 'success' },
        { label: '已关闭', value: 2, type: 'info' }
      ],
      typeOptions:[],
      loading: false,
      list: [],
      deliveryDialogVisible: false,
      downloadLoading: false,
      pages: {
        total: 0,
        limit: 20,
        current: 1
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.getList()
    },
    getList(page = this.pages.current, loading = true) {
      this.list = []
      if (this.loading) return
      this.loading = loading
      if (page === 1) this.pages.current = page
      dataList({ page, ...this.search, limit: this.pages.limit })
        .then((response) => {
          this.list = response.data.data.data
          this.pages.total = response.data.data.total
        })
        .finally(() => {
          this.loading = false
        })
    },
    onChangeDateRange(value) {
      if (Array.isArray(value)) {
        this.search.start_time = value[0]
        this.search.end_time = value[1]
      } else {
        this.search.start_time = this.search.end_time = ''
        this.getList(1)
      }
    },
    headNone({ row, colunm, rowIndex, columnIndex }) {
      if (rowIndex > 0) {
        return { display: 'none' }
      }
    },
    handleDelivery(row) {
      this.deliveryDialogVisible = true
      this.$nextTick(() => {
        this.$refs.deliveryDialog && this.$refs.deliveryDialog.init(row)
      })
    },
    onLogistics(row) {
      if (!row.logistic) {
        return this.$message.warning('无法查询')
      }
      if (row.logistic.logistics_code && row.logistic.logistics_no) {
        window.open(`http://www.kuaidi.com/all/${row.logistic.logistics_code}/${row.logistic.logistics_no}.html`, '_blank')
      }
    },
    onChangeStatus() {
      this.getList(1)
    },
    onHandleDownload() {
      this.downloadLoading = true
      exportExcel(this.search)
        .then((data) => {
          location.href = '/' + data.data.filename
        })
        .catch((_) => {})
        .finally(() => {
          this.downloadLoading = false
        })
    },
  }
}
</script>

<style lang='scss' scoped>
.info-wrapper {
  display: flex;
  .image-item {
    flex-shrink: 0;
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    img {
      height: auto;
    }
    ::v-deep .image-slot {
      font-size: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background: #f5f7fa;
      color: #909399;
    }
  }
}
::v-deep .el-table th {
  user-select: auto;
}
.order-table {
  margin-top: 20px;
}
.order-table .el-button + .el-button {
  margin-left: 0;
}
::v-deep .grid-content {
  color: #606266;
  font-size: 14px;
}
::v-deep .item .el-badge__content {
  transform: translateY(-50%) translateX(50%);
  z-index: 1;
}
::v-deep .el-radio-button:first-child .el-radio-button__inner {
  border-radius: 0;
  border-left: 0;
}
::v-deep
  .item:first-child
  .el-radio-button:first-child
  .el-radio-button__inner {
  border-left: 1px solid #dcdfe6;
  border-radius: 4px 0 0 4px;
}
::v-deep .item:last-child .el-radio-button:first-child .el-radio-button__inner {
  border-radius: 0 4px 4px 0;
}
::v-deep .item {
  .el-badge__content {
    transform: translateY(-50%) translateX(50%);
    z-index: 1;
  }
  &:first-child .el-radio-button:first-child .el-radio-button__inner {
    border-left: 1px solid #dcdfe6;
    border-radius: 4px 0 0 4px;
  }
  &:last-child .el-radio-button:first-child .el-radio-button__inner {
    border-radius: 0 4px 4px 0;
  }
}
::v-deep .el-radio-button:first-child .el-radio-button__inner {
  border-radius: 0;
  border-left: 0;
}
.goods-name{
    width: 170px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

</style>
