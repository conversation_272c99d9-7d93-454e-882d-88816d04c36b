<template>
  <div>
    <el-dialog title="发货" :visible.sync="visible" @closed="onClose()">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="商品信息">
          <div class="goods-box">
            <el-image
              class="img-box"
              fit="contain"
              :src="`${domin}${data.product_img}`"
              :preview-src-list="[
                `${domin}${data.product_img}`
              ]"
            />
            <div class="goods-info">
              <div class="goods-name">
                <span>
                  {{ data.product_name }}
                </span>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="物流公司" prop="logistics_no">
          <el-input
              v-model="form.logistics_name"
              clearable
              placeholder="请输入物流公司"
          />
        </el-form-item>
        <el-form-item label="快递单号" prop="logistics_no">
          <el-input
            v-model="form.logistics_no"
            clearable
            placeholder="请输入快递单号"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="btnLoading" @click="onFormSubmit()">
          {{ $t('table.confirm') }}
        </el-button>
        <el-button @click="visible = false">
          {{ $t('table.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken, DominKey } from '@/utils/auth'
import { goDelivery } from '@/api/shop-management'

export default {
  name: 'OrderDeliver',
  data() {
    return {
      domin: getToken(DominKey),
      btnLoading: false,
      visible: false,
      logisticsLoading: false,
      logisticsOptions: [],
      search: {
        keywords: ''
      },
      data: {},
      form: {
        order_id: 0,
        logistics_name: '',
        logistics_no: ''
      },
      rules: {
        logistics_name: [
          { required: true, message: '请选择', trigger: ['blur', 'change'] }
        ],
        logistics_no: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] }
        ]
      }
    }
  },
  methods: {
    init(data) {
      this.visible = true
      if (data) {
        this.form.order_no = data.order_no
        this.data = data
      }
    },
    onFormSubmit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.btnLoading = true
          goDelivery(this.form)
            .then(({ msg }) => {
              this.$message.success(msg)
              this.visible = false
              this.$emit('refreshList')
            })
            .catch(() => {
              this.btnLoading = false
            })
        }
      })
    },
    onClose() {
      this.$reset()
    }
  }
}
</script>

<style lang="scss" scoped>
.goods-box {
  display: flex;
  .img-box {
    width: 108px;
    height: 108px;
    flex-shrink: 0;
  }
  .goods-info {
    display: inline-block;
    margin-left: 10px;
    vertical-align: top;
    .goods-name {
      height: 80px;
      span {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
    div:nth-child(2) {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
