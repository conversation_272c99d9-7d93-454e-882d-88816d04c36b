<template>
  <div>
    <el-dialog top="30px" :title="`用户${form.name}的课程`" :visible.sync="visible" @closed="onClose()">
      <el-form :inline="true" :model="search">

        <el-form-item label="类型">
          <el-select v-model="search.type" clearable @change="getList(1)">
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="课程名称">
          <el-input v-model="search.keywords" placeholder="请输入名称" clearable @clear="getList(1)" @keyup.enter.native="getList(1)">
            <el-button slot="append" icon="el-icon-search" @click="getList(1)" />
          </el-input>
        </el-form-item>
      </el-form>
      <h4>拥有课程总数: {{ pages.total }}</h4>
      <el-table
        v-loading="loading"
        border
        highlight-current-row
        :data="list"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="60"
          align="center"
        />
        <el-table-column
          width="320"
          label="课程名称"
          header-align="center"
        >
          <template slot-scope="{ row }">
            <div class="goods-info">
              {{ row.course && row.course.name || '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="管理员"
          header-align="center"
        >
          <template slot-scope="{ row }">
            <div v-if="row.admin">
              {{ row.admin.name }}
            </div>
            <div v-else> - </div>
          </template>
        </el-table-column>
        <el-table-column
            prop="type"
            label="类型"
            width="60"
            align="center"
        >
          <template slot-scope="{ row: { type } }">
            <el-tag v-if="type == 'learn'" type="success">学习</el-tag>
            <el-tag v-else type="danger">收藏</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="添加时间"
          width="140"
          align="center"
        >
          <template slot-scope="{ row }">
            {{ row.created_at || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="100"
          align="center"
          fixed="right"
        >
          <template slot-scope="{ row, $index }">
            <el-button-group>
              <el-button type="danger" @click="onDelete(row, row.id)">删除</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="pages.total > 0" :total="pages.total" :page.sync="pages.current" :limit.sync="pages.limit" @pagination="getList()" />
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { DominKey, getToken } from '@/utils/auth'
import {userCollection, userCourseDelete} from '@/api/user'
export default {
  name: 'CollectionUpdate',
  components: { Pagination },
  data() {
    return {
      typeOptions: [
        { label: '全部', value: '' },
        { label: '学习', value: 'learn' },
        { label: '收藏', value: 'collect' }
      ],
      visible: false,
      loading: false,
      list: [],
      domin: getToken(DominKey),
      pages: {
        total: 0,
        limit: 20,
        current: 1
      },
      search: {
        type: '',
        keywords: ''
      },
      form: {
        id: '',
        name: ''
      }
    }
  },
  methods: {
    init(data) {
      this.visible = true
      this.form.id = data.id
      this.form.name = data.name
      this.getList()
    },
    getList(page = this.pages.current, loading = true) {
      if (this.loading) return
      this.loading = loading
      if (page === 1) this.pages.current = page
      this.list = []
      userCollection(this.form.id, { page, limit: this.pages.limit,user_id:this.form.id, ...this.search })
        .then(response => {
          if (response.code !== 200) return
          this.list = response.data.data
          this.pages.total = response.data.total || 0
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    onClose() {
      this.visible = false
    },
    onDelete(row, index) {
      userCourseDelete(index)
          .then(({ msg = '删除成功' }) => {
            this.$message.success(msg)
            this.getList()
          })
          .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep .el-dialog {
    width: 850px;
  }
  .goods-box {
    display: flex;
    .img-box {
      flex-shrink: 0;
    }
    .goods-info {
      display: inline-block;
      margin-left: 10px;
      vertical-align: top;
      .goods-name {
        height: 80px;
        span {
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }
      div:nth-child(2) {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .goods-info {
    display: inline-block;
    margin-left: 10px;
    vertical-align: top;

    .goods-name {
      width: 185px;
      height: 60px;

      span {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }
    }

    div:nth-child(2) {
      width: 169px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  $bg:#2d3a4b;
  $dark_gray:#889aa4;
  $light_gray:#eee;

  .login-container {
    height: 100%;
    width: 100%;
    background-color: $bg;
    overflow: hidden;

    .login-content {
      height: 100%;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .svg-container {
      padding: 6px 5px 6px 15px;
      color: $dark_gray;
      vertical-align: middle;
      width: 30px;
      display: inline-block;
    }

    .title-container {
      position: relative;

      .title {
        font-size: 26px;
        color: $light_gray;
        margin: 0px auto 40px auto;
        text-align: center;
        font-weight: bold;
      }

      .set-language {
        color: #fff;
        position: absolute;
        top: 3px;
        font-size: 18px;
        right: 0px;
        cursor: pointer;
      }
    }

    .show-pwd {
      position: absolute;
      right: 10px;
      top: 7px;
      font-size: 16px;
      color: $dark_gray;
      cursor: pointer;
      user-select: none;
    }

    .thirdparty-button {
      position: absolute;
      right: 0;
      bottom: 6px;
    }

    @media only screen and (max-width: 470px) {
      .thirdparty-button {
        display: none;
      }
    }
  }
</style>
