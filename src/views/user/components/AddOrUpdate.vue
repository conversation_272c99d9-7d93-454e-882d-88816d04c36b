<template>
  <el-dialog top="30px" :title="$t('table.edit')" :visible.sync="visible" @closed="onClose()">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="讲师/推荐人" prop="is_lecturer">
        <el-radio-group v-model="form.is_lecturer">
          <el-radio :label="0">否</el-radio>
          <el-radio :label="1">是</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="状态" prop="state">
        <el-radio-group v-model="form.state">
          <el-radio :label="0">正常</el-radio>
          <el-radio :label="1">禁用</el-radio>
        </el-radio-group>
        <div class="notice1">
          注意：禁用之后用户无法登录
        </div>
      </el-form-item>
       <el-form-item label="插件权限" prop="permissions">
        <el-tree
          v-if="form.id > 0"
          ref="plug-tree"
          v-loading="treeLoading"
          node-key="id"
          show-checkbox
          :data="plugList"
          :default-expanded-keys="form.plug"
          :check-on-click-node="true"
          :expand-on-click-node="false"
          :props="{ label: 'name', children: 'children' }"
        />
      </el-form-item>
      
       <el-form-item label="课程权限" prop="permissions">
        <el-tree
          v-if="form.id > 0"
          ref="course-tree"
          v-loading="treeLoading"
          node-key="id"
          show-checkbox
          :data="courseList"
          :default-expanded-keys="form.course"
          :check-on-click-node="true"
          :expand-on-click-node="false"
          :props="{ label: 'name', children: 'children' }"
        />
      </el-form-item>
      
       <el-form-item label="周边权限" prop="permissions">
        <el-tree
          v-if="form.id > 0"
          ref="goods-tree"
          v-loading="treeLoading"
          node-key="id"
          show-checkbox
          :data="goodsList"
          :default-expanded-keys="form.goods"
          :check-on-click-node="true"
          :expand-on-click-node="false"
          :props="{ label: 'name', children: 'children' }"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="btnLoading" @click="onFormSubmit()">
        {{ $t('table.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('table.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { DominKey, getToken } from '@/utils/auth'
import { addOrUpdate, dataCateTreeList } from '@/api/user'
import CustomUpload from '@/components/Upload/CustomUpload'
import { listTree } from '@/utils'
// import { dataList } from '@/api/menu'

export default {
  name: 'AddOrUpdate',
  components: { CustomUpload },
  data() {
    return {
      visible: false,
      btnLoading: false,
      domin: getToken(DominKey),
      menuList: [],
      btnLoading: false,
      treeLoading: false,
      plugList:[],
      courseList:[],
      goodsList:[],
      form: {
        id: 0,
        avatar: '',
        name: '',
        account: '',
        is_lecturer: 0,
        state: 0,
        plug:[],
        goods:[],
        course:[],
        permissions:[],
      },
      rules: {
      }
    }
  },
  methods: {
    

    init(data) {

      this.visible = true
      if (data) {
        console.log(data, 'data-----------');
        const newKeys = Object.keys(this.form).filter(v => {
          return !['password', 'password_confirmation'].includes(v)
        })
        newKeys.map(v => {
          if (v === 'account') {
            this.form[v] = data['phone']
          } else {
            this.form[v] = data[v]
          }
        })
        this.getMenusHandle()
        // this.form.permissions = data.permissions ? data.permissions.map(({ id }) => id).concat(): []
      }
    },
    
    getMenusHandle() {
      if (this.treeLoading) return
      this.treeLoading = true
      dataCateTreeList()
        .then(({ data }) => {
          this.formatMenuData(data)
          this.treeLoading = false
        })
        .catch(() => {
          this.treeLoading = false
        })
    },
    
    formatMenuData(menus) {
      var plugl = menus.plug;
      var coursel = menus.course;
      var goodsl = menus.goods;
      console.log(plugl, 'this.plugl--------------');
      this.plugList = listTree(plugl, 'id', 'parent_id').map(v => {
          console.log(v, 'v--------------');
          if (v.alias === 'tenant') {
            v.children.forEach((item, index) => {
              v.children[index] = { ...item, disabled: true }
            })
            return { ...v, disabled: true }
          }
          return v
      })
      
      this.courseList = listTree(coursel, 'id', 'parent_id').map(v => {
          console.log(v, 'v--------------');
          if (v.alias === 'tenant') {
            v.children.forEach((item, index) => {
              v.children[index] = { ...item, disabled: true }
            })
            return { ...v, disabled: true }
          }
          return v
      })
      
      this.goodsList = listTree(goodsl, 'id', 'parent_id').map(v => {
          console.log(v, 'v--------------');
          if (v.alias === 'tenant') {
            v.children.forEach((item, index) => {
              v.children[index] = { ...item, disabled: true }
            })
            return { ...v, disabled: true }
          }
          return v
      })

      // console.log(this.plugList, 'this.menus--------------');
      // console.log(this.menuList, 'this.menuList--------------');
      if (this.form.goods && this.form.goods.length > 0) {
        this.setCheckedKeys(
          this.filterFullCheckedKeys(this.goodsList, this.form.goods), 'goods-tree'
        )
      }
      if (this.form.course && this.form.course.length > 0) {
        console.log(this.courseList, 'this.courseList--------------');
        console.log(this.form.course, 'this.courseList--------------');
        this.setCheckedKeys(
          this.filterFullCheckedKeys(this.courseList, this.form.course), 'course-tree'
        )
      }
      if (this.form.plug && this.form.plug.length > 0) {
        this.setCheckedKeys(
          this.filterFullCheckedKeys(this.plugList, this.form.plug), 'plug-tree'
        )
      }
    },


    setCheckedKeys(keys = [], byref = 'menu-tree') {
      console.log(keys, byref, 'byref-------------');
      this.$refs[byref].setCheckedKeys(keys)
    },
    getAllCheckedKeys(byref = 'menu-tree') {
      const tree = this.$refs[byref]
      return [...tree.getCheckedKeys(), ...tree.getHalfCheckedKeys()]
    },
    filterFullCheckedKeys(list, checked = [], full = []) {
      
      if (!checked.length > 0) return full

      list.forEach(item => {
        const { id, children } = item
        if (!checked.includes(id)) return
        const len = Array.isArray(children) ? children.length : 0
        len
          ? this.filterFullCheckedKeys(children, checked, full)
          : full.push(id)
      })

      return full
    },

    onFormSubmit() {
      this.form.goods = this.getAllCheckedKeys("goods-tree");
      this.form.plug = this.getAllCheckedKeys("plug-tree");
      this.form.course = this.getAllCheckedKeys("course-tree");
      
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.btnLoading = true



          const data = Object.assign({}, this.form)
          addOrUpdate(data)
            .then(({ msg }) => {
              this.$message.success(msg)
              this.visible = false
              this.$emit('refreshList')
            })
            .catch(() => {})
            .finally(() => {
              this.btnLoading = false
            })
        }
      })
    },
    onClose() {
      this.$reset()
    },
    handleAvatarSuccess(response, file) {
      this.form.avatar = response.name
    },
    beforeAvatarUpload(file, cb) {
      const type = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
      const isLt2M = file.size / 1024 / 1024 < 20

      if (!type.includes(file.type)) {
        this.$message.error('上传图片只能是 ' + type.join(',') + ' 格式!')
        cb(false)
        return
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 20M')
        cb(false)
        return
      }
      cb(true)
    }
  }
}
</script>

<style lang="scss" scoped>
.avatar-uploader {
  display: inline-block;
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
    object-fit: contain;
  }
}
.notice {
  color: #909399;
  font-size: 12px;
}
.notice1 {
  color: #e6a23c;
  font-size: 12px;
}
</style>
