<template>
  <el-dialog top="30px" :title="$t('table.edit')" :visible.sync="visible" @closed="onClose()">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="form.phone" placeholder="手机号" />
      </el-form-item>
      <el-form-item label="课程" prop="course_id">
        <el-select v-model="form.course_id" filterable placeholder="请选择课程">
          <el-option
              v-for="(item, index) in courseOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
          >
            <span>{{ item.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="btnLoading" @click="onFormSubmit()">
        {{ $t('table.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('table.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { DominKey, getToken } from '@/utils/auth'
import {addUserCourse, courseList} from '@/api/user'

export default {
  name: 'AddCourse',
  data() {
    return {
      visible: false,
      btnLoading: false,
      domin: getToken(DominKey),
      courseOptions:[],
      form: {
        id: 0,
        phone: '',
        course_id: 0,
      },
    }
  },
  methods: {
    init(data) {
      this.visible = true
      this.courseList()
      if (data) {
        const newKeys = Object.keys(this.form).filter(v => {
          return !['password', 'password_confirmation'].includes(v)
        })
        newKeys.map(v => {
          if (v === 'account') {
            this.form[v] = data['phone']
          } else {
            this.form[v] = data[v]
          }
        })
      }
    },
    onFormSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.btnLoading = true
          const data = Object.assign({}, this.form)
          addUserCourse(data)
            .then(({ msg }) => {
              this.$message.success(msg)
              this.visible = false
              this.$emit('refreshList')
            })
            .catch(() => {})
            .finally(() => {
              this.btnLoading = false
            })
        }
      })
    },
    onClose() {
      this.$reset()
    },
    courseList() {
      courseList().then(response => {
        this.courseOptions = response.data.map(v => {
          return {
            label: v.name,
            value: v.id,
          }
        })
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.avatar-uploader {
  display: inline-block;
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
    object-fit: contain;
  }
}
.notice {
  color: #909399;
  font-size: 12px;
}
.notice1 {
  color: #e6a23c;
  font-size: 12px;
}
</style>
