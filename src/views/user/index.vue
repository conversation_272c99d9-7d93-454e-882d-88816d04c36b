<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" :model="search">
        <el-form-item label="账号">
          <el-input v-model="search.keywords" placeholder="ID/昵称/手机号" clearable @clear="getList(1)"
                    @keyup.enter.native="getList(1)"/>
        </el-form-item>
        <el-form-item label="是否禁用">
          <el-select v-model="search.state" clearable @change="getList(1)">
            <el-option v-for="item in whetherOptions" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
              v-model="dateRangeValue"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :picker-options="pickerOptions"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="onChangeDateRange"
          />
        </el-form-item>
        <el-form-item label="排序方式">
          <el-select v-model="search.type" clearable placeholder="请选择排序方式" @change="getList(1)">
            <el-option v-for="item in sortOptions" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-button icon="el-icon-search" @click="getList(1)">
          {{ $t('table.search') }}
        </el-button>
        <el-button :loading="downloadLoading" type="success" icon="el-icon-document" @click="onHandleDownload">
          {{ $t('table.export') }} Excel
        </el-button>
        <br><br>
        <el-button :loading="downloadLoading" type="success" icon="el-icon-document" @click="onAddCourse">
          用户课程添加
        </el-button>
        <el-button type="warning" icon="el-icon-coin" @click="onRechargeManage">
          光子充值
        </el-button>
      </el-form>
    </div>
    <el-table
        v-loading="loading"
        border
        :data="list"
    >
      <el-table-column
          prop="id"
          label="ID"
          width="80"
          align="center"
      />
      <el-table-column
          label="账号"
          min-width="180"
          header-align="center"
      >
        <template slot-scope="{ row }">
          <el-avatar :key="row.id" icon="el-icon-user-solid" style="vertical-align: top;" :src="row.avatar"/>
          <div style="display: inline-block;margin-left: 2px">
            <div>
              {{ row.name }}
            </div>
            <div>
              {{ row.phone }}
            </div>
            <div/>
          </div>
        </template>
      </el-table-column>

      <el-table-column
          prop="state"
          label="状态"
          width="60"
          align="center"
      >
        <template slot-scope="{ row: { state } }">
          <el-tag v-if="state === 0" type="success">正常</el-tag>
          <el-tag v-else type="danger">禁用</el-tag>
        </template>
      </el-table-column>
      <!--      创作者-->
      <el-table-column
          prop="is_lecturer"
          label="讲师"
          width="60"
          align="center"
      >
        <template slot-scope="{ row: { is_lecturer } }">
          <el-tag v-if="is_lecturer === 0" type="success">否</el-tag>
          <el-tag v-else type="danger">是</el-tag>
        </template>
      </el-table-column>
      <!--      其它-->
      <el-table-column
          label="其它"
          min-width="180"
          header-align="center"
      >
        <template slot-scope="{ row }">
          <div style="display: inline-block;margin-left: 2px">
            <div>
              QQ：{{ row.qq }}
            </div>
            <div>
              email：{{ row.email }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="资产" min-width="180" header-align="center">
        <template slot-scope="{ row }">
          <div style="display: inline-block;margin-left: 2px">
            <div>累计：{{ row.income }}</div>
            <div>余额：{{ row.balance }}</div>
            <div>支出：{{ row.expend }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
          label="签名"
          min-width="180"
          header-align="center"
      >
        <template slot-scope="{ row }">
          <div style="display: inline-block;margin-left: 2px">
            {{ row.sign }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
          label="简介"
          min-width="180"
          header-align="center"
      >
        <template slot-scope="{ row }">
          <div style="display: inline-block;margin-left: 2px">
            {{ row.intro }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
          prop="email_verified_at"
          label="邮箱验证"
          width="140"
          align="center"
      />
      <el-table-column
          prop="created_at"
          label="创建时间"
          width="140"
          align="center"
      />
      <el-table-column
          label="操作"
          width="340"
          fixed="right"
          align="center"
      >
        <template slot-scope="{ row }">
          <el-button-group>
            <el-button type="primary" @click="clearWallet(row)">资产扣除</el-button>
            <el-button plain type="primary" @click="onUserCourse(row)">课程列表</el-button>
            <el-button type="primary" @click="onAddOrUpdate(row)">编辑</el-button>
            <el-button type="danger" @click="onDelete(row)">删除</el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="pages.total > 0" :total="pages.total" :page.sync="pages.current" :limit.sync="pages.limit"
                @pagination="getList()"/>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
        v-if="addOrUpdateVisible"
        ref="addOrUpdate"
        @refreshList="getList()"
    />
    <!-- 添加课程 -->
    <add-course
        v-if="addCourseVisible"
        ref="addCourse"
        @refreshList="getList()"
    />

    <!-- 他的课程-->
    <collection-update
        v-if="collectionUpdateVisible"
        ref="collection"
        @refreshList="getList()"
    />

    <!-- 光子充值管理 -->
    <recharge-manage
        v-if="rechargeManageVisible"
        ref="rechargeManage"
        @refreshList="getList()"
    />

  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import {clearWallet, dataList, deleteData, exportOrder} from '@/api/user'
import AddOrUpdate from './components/AddOrUpdate'
import AddCourse from './components/AddCourse'
import CollectionUpdate from './components/CollectionUpdate'
import RechargeManage from '@/components/RechargeManage'
import {getToken, DominKey} from '@/utils/auth'
import {pickerOptions, whetherOptions} from '@/utils/explain'

export default {
  name: 'User',
  components: {Pagination, AddOrUpdate, CollectionUpdate, AddCourse, RechargeManage},
  data() {
    return {
      domin: getToken(DominKey),
      pickerOptions,
      dateRangeValue: [],
      list: [],
      search: {
        keywords: '',
        start_time: '',
        end_time: '',
        state: '',
        type: '',
      },
      pages: {
        total: 0,
        limit: 20,
        current: 1
      },
      whetherOptions,
      sortOptions: [
        { label: '累计', value: 'income' },
        { label: '余额', value: 'balance' },
        { label: '支出', value: 'expend' }
      ],
      loading: false,
      addOrUpdateVisible: false,
      addCourseVisible: false,
      downloadLoading: false,
      collectionUpdateVisible: false,
      rechargeManageVisible: false,
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.getList()
    },
    getList(page = this.pages.current, loading = true) {
      if (this.loading) return
      this.loading = loading
      if (page === 1) this.pages.current = page
      this.list = []
      dataList({page, ...this.search, limit: this.pages.limit})
          .then(response => {
            if (response.code !== 200) return
            this.list = response.data.data
            this.pages.total = response.data.total
          })
          .catch(() => {
          })
          .finally(() => {
            this.loading = false
          })
    },
    onChangeDateRange(value) {
      if (Array.isArray(value)) {
        this.search.start_time = value[0]
        this.search.end_time = value[1]
      } else {
        this.search.start_time = this.search.end_time = ''
        this.getList(1)
      }
    },
    onAddOrUpdate(data) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate && this.$refs.addOrUpdate.init(data)
      })
    },
    onAddCourse(data) {
      this.addCourseVisible = true
      this.$nextTick(() => {
        this.$refs.addCourse && this.$refs.addCourse.init(data)
      })
    },
    onDelete({name, id}) {
      this.$confirm(`确定对(#${id})[${name}]进行[删除]操作？`, '删除', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            dangerouslyUseHTMLString: true,
            type: 'error'
          }
      )
          .then(() => {
            deleteData(id)
                .then(({msg = '删除成功'}) => {
                  this.$message.success(msg)
                  this.init()
                })
                .catch(() => {
                })
          })
          .catch(() => {
          })
    },
    clearWallet({name, id}) {
      this.$confirm(`确定对(#${id})[${name}]进行[资产清除]操作？`, '资产清除', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            dangerouslyUseHTMLString: true,
            type: 'error'
          }
      ).then(() => {
        clearWallet(id).then(({msg = '资产清除'}) => {
          this.$message.success(msg)
          this.init()
        }).catch(() => {
        })
      }).catch(() => {
      })
    },
    onHandleDownload() {
      this.downloadLoading = true
      exportOrder(this.search)
          .then((data) => {
            location.href = '/' + data.data.filename
          })
          .catch(_ => {
          })
          .finally(_ => {
            this.downloadLoading = false
          })
    },
    onUserCourse(data) {
      this.collectionUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.collection && this.$refs.collection.init(data)
      })
    },
    onRechargeManage() {
      this.rechargeManageVisible = true
      this.$nextTick(() => {
        this.$refs.rechargeManage && this.$refs.rechargeManage.init()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.ellipsis {
  width: 100%;
}

::v-deep .ellipsis span {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
