<template>
	<div class="app-container">
		<el-form ref="form" :model="form" :rules="rules" label-width="160px">
		  <template>
			<el-form-item label="封面图" prop="images">
			  <custom-upload
				  class-name="avatar-uploader"
				  ref-name="images"
				  @handleBeforeUpload="beforeAvatarUpload"
				  @handleSuccess="handleAvatarSuccess"
			  >
				<img v-if="form.images" :src="domin + form.images" class="avatar">
				<i v-else class="el-icon-plus avatar-uploader-icon" />
			  </custom-upload>
			  <div class="notice">
				注意：建议封面图尺寸 750*750px
			  </div>
			</el-form-item>
		  </template>
		  <el-form-item label="视频介绍" prop="link">
		    <custom-upload
		        class-name=""
		        :show-file-list="true"
		        :limit="1"
		        @handleBeforeUpload="beforeFileUpload"
		        @handleSuccess="handleFileSuccess"
		        @handleExceed="handleExceed"
		        @elProgress="elProgress"
		    >
		      <el-button type="primary">点击上传</el-button>
		    </custom-upload>
		    <el-progress v-if="![0, 1].includes(percentage)" :percentage="percentage * 100" />
		  </el-form-item>
		  <el-form-item label="插件名称" prop="name">
			<el-input v-model="form.name" placeholder="课程名称" />
		  </el-form-item>
		  <!-- <el-form-item label="价格" prop="price">
			<el-input-number v-model="form.price" :precision="2" :min="0" placeholder="价格" /> <span style="color: red">光子</span>
		  </el-form-item>
		  <el-form-item label="插件下载量" prop="download_package">
			<el-input-number v-model="form.download_package" :precision="0" :min="0" placeholder="插件下载量" />
		  </el-form-item>
		  <el-form-item label="手册下载量" prop="download_manual">
			<el-input-number v-model="form.download_manual" :precision="0" :min="0" placeholder="手册下载量" />
		  </el-form-item>
		  <el-form-item label="案列下载量" prop="download_case">
			<el-input-number v-model="form.download_case" :precision="0" :min="0" placeholder="案列下载量" />
		  </el-form-item> -->
		  <el-form-item label="手册/下载" prop="intro">
			<el-link type="primary" :underline="false" @click="onDetail(form)">点击编辑</el-link>
		  </el-form-item>
		  <el-form-item label="功能介绍" prop="web_link">
			<el-input v-model="form.web_link" placeholder="网站链接" />
		  </el-form-item>
		</el-form>
		<div slot="footer" class="dialog-footer">
		  <el-button type="primary" :loading="btnLoading" @click="onFormSubmit()">
			{{ $t('table.confirm') }}
		  </el-button>
		  <!-- <el-button @click="visible = false">
			{{ $t('table.cancel') }}
		  </el-button> -->
		</div>

		<el-image-viewer
			v-if="imageViewer"
			:z-index="3000"
			:on-close="closeViewer"
			:url-list="imageViewerList"
		/>
		<plug-detail
		    v-if="plugDetailVisible"
		    ref="plugDetail"
		    @refreshList="getDetails()"
		/>
	</div>
    
</template>

<script>
import CustomUpload from '@/components/Upload/CustomUpload'
import {plugAddOrUpdate, plugDetails} from '@/api/ai'
import { DominKey, BindKey, getToken } from '@/utils/auth'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import PlugDetail from "./components/PlugDetail";
export default {
  name: 'aiPlug',
  components: { CustomUpload, ElImageViewer , PlugDetail},
  data() {
    return {
		percentage: 0,
      visible: false,
      btnLoading: false,
      editTinymceVisible: false,
      imageViewer: false,
      imageViewerList: [],
      domin: getToken(DominKey),
      accountBind: getToken(BindKey),
      currentName: '',
      form: {
		id: 1,
        name: '',
        images: '',
        price: 0,
        send: 0,
        market_price: 0,
        download_package: 0,
        download_manual: 0,
        download_case: 0,
        web_link: '',
		link: '',
      },
      rules: {
  //       images: [
  //         { required: true, message: '请选择封面图', trigger: ['blur', 'change'] }
  //       ],
		// link: [
		//   { required: true, message: '请上传介绍视频', trigger: ['blur', 'change'] }
		// ],
        
      },
	  plugDetailVisible:false
    }
  },
  computed: {
    dragOptions() {
      return {
        animation: 200,
        group: 'description',
        disabled: false,
        ghostClass: 'ghost'
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
		this.visible = true
		this.getDetails()
    },
	onDetail(data) {
	  this.plugDetailVisible = true
	  this.$nextTick(() => {
	    this.$refs.plugDetail && this.$refs.plugDetail.init(data)
	  })
	},
    getDetails() {
      plugDetails(this.form.id)
          .then(response => {
            this.form = response.data
          })
          .catch(() => {})
    },
    onFormSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id) {
            this.$confirm(`确定对[(#${this.form.id})]进行修改?`, '编辑', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
                .then(() => {
                  this.hanldaddOrUpdate()
                })
                .catch(() => {
                })
          } else {
            this.hanldaddOrUpdate()
          }
        }
      })
    },
    hanldaddOrUpdate() {
      const data = JSON.parse(JSON.stringify(this.form))
      this.btnLoading = true
      plugAddOrUpdate(data)
          .then(({ msg }) => {
            this.$message.success(msg)
            this.visible = false
			this.btnLoading = false
			
            this.$emit('refreshList')
          })
          .catch(() => {
            this.btnLoading = false
          })
    },
    
    onInfo(value) {
      this.form.intro = value
    },
    onClose() {
      this.$reset()
    },
    closeViewer() {
      this.imageViewer = false
    },
    beforeAvatarUpload3d(file, cb, refName) {
      const isLt20M = file.size / 1024 / 1024 < 20
      if (!isLt20M) {
        this.$message.error('上传文件大小不能超过 20M')
        cb(false)
        return
      }
      this.currentName = refName
      cb(true)
    },
    handleAvatarSuccess(response, file) {
      this.form[this.currentName] = response.name
    },
    beforeAvatarUpload(file, cb, refName) {
      const type = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
      const isLt2M = file.size / 1024 / 1024 < 20
      if (!type.includes(file.type)) {
        this.$message.error('上传图片只能是 ' + type.join(',') + ' 格式!')
        cb(false)
        return
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 20M')
        cb(false)
        return
      }
      this.currentName = refName
      cb(true)
    },
	// 上传文件
	handleFileSuccess(response, file) {
	  this.form.link = response.name
	},
	beforeFileUpload(file, cb, refName) {
	  const isLt2M = file.size / 1024 / 1024 < 2048
	  if (!isLt2M) {
	    this.$message.error('上传文件大小不能超过 2048M')
	    cb(false)
	    return
	  }
	  this.currentName = refName
	  cb(true)
	},
	elProgress(p) {
	  this.percentage = p
	},
    handleError() {
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件，请删除后在上传`)
    }
  }
}
</script>
<style lang="scss" scoped>
.el-input-number {
  width: 200px;
}
.box-card {
  margin-bottom: 18px;
}
.avatar-uploader {
  display: inline-block;
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
    object-fit: contain;
  }
}
::v-deep .filter-list-box {
  .wrapper {
    display: inline;
    vertical-align: top;
  }
  .upload-images {
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #c0ccda;
    border-radius: 6px;
    box-sizing: border-box;
    width: 100px;
    height: 100px;
    margin: 0 8px 8px 0;
    display: inline-block;
    position: relative;
    cursor: grabbing;
    .upload-image {
      width: 100%;
      height: 100%;
      display: -webkit-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      -webkit-justify-content: center;
      justify-content: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      -webkit-align-items: center;
      align-items: center;
      & > img {
        width: 100%;
      }
    }
    .upload-actions {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background-color: rgba(0,0,0,0.5);
      text-align: center;
      display: none;
      i {
        margin-left: 6px;
        margin-top: 6px;
        &:first-child {
          margin-left: 0;
        }
      }
    }
    &:hover .upload-actions {
      display: block;
    }
    .upload-actions i {
      color: #fff;
      font-size: 18px;
      cursor: pointer;
    }
  }
}
.notice {
  margin: 10px 0 0 0;
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
}
</style>
