<template>
  <div>
    <el-dialog class="el-dialog-wallet" title="插件详情" top="30px" :visible.sync="visible" @closed="onClose()">
      <el-row style="margin-bottom: 10px">
        <el-col :span="12">
          <el-button type="primary" size="mini" @click="onAddOrUpdate(plug)">添加</el-button>
        </el-col>
      </el-row>
      <p />
      <div class="filter-container">
        <el-form :inline="true" :model="search">
          <el-form-item label="类型">
            <el-select v-model="search.type" @clear="getList(1)" @change="getList(1)">
              <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <el-table
          v-loading="loading"
          border
          highlight-current-row
          :data="list"
      >
        <el-table-column
            prop="id"
            label="ID"
            width="80"
            align="center"
        />
        <el-table-column
            prop="version_code"
            label="版本号"
            align="center"
        />
        <el-table-column
            prop="platform"
            label="支持平台"
            align="center"
        />
        <el-table-column
            prop="type"
            label="类型"
            align="center"
        >
          <template slot-scope="{ row }">
            <div> {{ row.type | paraphrase(typeOptions) }}</div>
          </template>
        </el-table-column>
        <el-table-column
            prop="description"
            label="描述"
            align="center"
        >
          <template slot-scope="{ row }">
            <div> {{  row.description }}</div>
          </template>
        </el-table-column>
        <el-table-column
            prop="link"
            label="文件"
            align="center"
        >
          <template slot-scope="{ row }">
            <div> {{  row.link ? domin + row.link : '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column
            prop="sort"
            label="排序"
            width="60"
            align="center"
        />
        <el-table-column
            prop="created_at"
            label="添加时间"
            width="140"
            align="center"
        />
        <el-table-column
            label="操作"
            width="220"
            align="center"
            fixed="right"
        >
          <template slot-scope="{ row, $index }">
            <el-button-group>
              <el-button type="primary" @click="onAddOrUpdate(row)">编辑</el-button>
              <el-button type="danger" @click="onDelete(row, $index)">删除</el-button>
            </el-button-group>
            <br>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="pages.total > 0" :total="pages.total" :page.sync="pages.current" :limit.sync="pages.limit" @pagination="getList()" />
    </el-dialog>
    <!-- 添加 编辑 -->
    <add-or-update
        v-if="addOrUpdateVisible"
        ref="addOrUpdate"
        @refreshList="getList()"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getPlugDetailLists, plugDetailDelete} from '@/api/ai'
import AddOrUpdate from './DetailAddOrUpdate'
import { DominKey, getToken } from '@/utils/auth'
import { pickerOptions } from '@/utils/explain'

export default {
  name: 'PlugDetail',
  components: { Pagination,AddOrUpdate },
  data() {
    return {
      domin: getToken(DominKey),
      visible: false,
      downloadLoading: false,
      plug: {},
      data: {
        id: 0,
        version_code: '',
        platform: '',
        description: '',
        link: '',
        type: '',
        sort: 0,
      },
      list: [],
      search: {
        type: '',
        start_time: '',
        end_time: '',
      },
      pages: {
        total: 0,
        limit: 20,
        current: 1
      },
      typeOptions: [
        { label: '全部', value: '' },
        { label: '下载', value: 'package' },
        { label: '手册', value: 'manual' },
        { label: '案列', value: 'case' },
      ],
      dateRangeValue: [],
      pickerOptions,
      loading: false,
      addOrUpdateVisible: false
    }
  },
  methods: {
    init(data) {
      this.visible = true
      if (data) {
        this.plug = data
        this.search.id = data.id
        this.getList()
      }
    },
    getList(page = this.pages.current, loading = true) {
      if (this.loading) return
      this.loading = loading
      if (page === 1) this.pages.current = page
      this.list = []
      getPlugDetailLists({ plug_id:this.plug.id, page, ...this.search, limit: this.pages.limit })
          .then(response => {
            if (response.code !== 200) return
            this.list = response.data.data
            this.pages.total = response.data.total
          })
          .catch(() => {})
          .finally(() => {
            this.loading = false
          })
    },
    onChangeDateRange(value) {
      if (Array.isArray(value)) {
        this.search.start_time = value[0]
        this.search.end_time = value[1]
      } else {
        this.search.start_time = this.search.end_time = ''
        this.getList(1)
      }
    },
    onClose() {
      this.$reset('data', false)
    },
    onAddOrUpdate(data) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate && this.$refs.addOrUpdate.init(data)
      })
    },
    onDelete(row, index) {
      this.$confirm(
          `确定进行[删除]操作?`,
          '删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'error',
            cancelButtonClass: 'btn-custom-cancel'
          }
      )
          .then(() => {
            plugDetailDelete(row.id)
                .then(({ msg = '删除成功' }) => {
                  this.$message.success(msg)
                  this.getList()
                })
                .catch(() => {})
          })
          .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog-wallet .el-dialog {
  width: 1240px;
}
::v-deep .user-avatar{
  vertical-align: middle;
}
</style>
