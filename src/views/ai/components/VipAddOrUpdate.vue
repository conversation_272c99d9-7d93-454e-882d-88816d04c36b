<template>
    <el-dialog top="30px" width="1000px" :title="form.id ? $t('table.edit') : $t('table.add') " :visible.sync="visible" :close-on-click-modal="false" :close-on-press-escape="false" @closed="onClose()">
      <el-form ref="form" :model="form" :rules="rules" label-width="160px">
          <el-form-item label="用户" prop="user_id" v-if="form.id">
              {{form.user.name}}
          </el-form-item>
          <el-form-item label="用户" prop="user_id" v-else>
            <el-select v-model="form.user_id" filterable placeholder="请选择用户">
              <el-option
                  v-for="(item, index) in userOptions"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
              >
                <span>#{{ item.value }}</span>
                <span>
                    {{ item.label }}
                  </span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否永久VIP" prop="permanent">
            <el-select v-model="form.permanent" filterable placeholder="请选择">
              <el-option
                  v-for="(item, index) in permanentOptions"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
              >
                <span>
                    {{ item.label }}
                  </span>
              </el-option>
            </el-select>
          </el-form-item>
        <el-form-item label="到期时间" prop="endtime">
            <el-date-picker
                  v-model="form.endtime"
                  type="date"
                  placeholder="选择日期">
            </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="btnLoading" @click="onFormSubmit()">
          {{ $t('table.confirm') }}
        </el-button>
        <el-button @click="visible = false">
          {{ $t('table.cancel') }}
        </el-button>
      </div>
  
     
  
    </el-dialog>
  </template>
  
  <script>
  import {vipAddOrUpdate, vipDetails} from '@/api/ai'
  import {userList} from '@/api/user'
  import { DominKey, getToken } from '@/utils/auth'
  import {authorList} from "@/api/user";
  export default {
    name: 'AddOrUpdate',
    components: {},
    data() {
      return {
        visible: false,
        btnLoading: false,
        domin: getToken(DominKey),
        currentName: '',
        userOptions:[],
        permanentOptions:[
          {
              label : '否',
              value : 0
          },
          {
              label : '是',
              value : 1
          },
        ],
        form: {
          user_id: '',
          permanent: '',
          endtime: '',
          user:{
              name:''
          }
        },
        rules: {
          user_id: [
            { required: true, message: '请选择用户', trigger: ['blur', 'change'] }
          ]
        },
      }
    },
    computed: {
      
    },
    methods: {
      init(data) {
          this.userLists()
          
        this.visible = true
        if (data) {
          this.form.id = data.id
          this.getDetails()
        }
      },
      getDetails() {
        vipDetails(this.form.id)
            .then(response => {
              this.form = response.data
            })
            .catch(() => {})
      },
      onFormSubmit() {
        this.$refs['form'].validate(valid => {
          if (valid) {
            if (this.form.id) {
              this.$confirm(`确定对[(#${this.form.id})]进行修改?`, '编辑', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              })
                  .then(() => {
                    this.hanldaddOrUpdate()
                  })
                  .catch(() => {
                  })
            } else {
              this.hanldaddOrUpdate()
            }
          }
        })
      },
      hanldaddOrUpdate() {
        this.form.issuer_id = this.form.issuer_id || 0
        const data = JSON.parse(JSON.stringify(this.form))
        this.btnLoading = true
        vipAddOrUpdate(data)
            .then(({ msg }) => {
              this.$message.success(msg)
              this.visible = false
              this.$emit('refreshList')
            })
            .catch(() => {
              this.btnLoading = false
            })
      },
      onInfo(value) {
        this.form.content = value
      },
      onClose() {
        this.$reset()
      },
      userLists() {
        userList().then(response => {
          this.userOptions = response.data.map(v => {
            return {
              label: v.name,
              value: v.id,
            }
          })
        })
      },
    }
  }
  </script>
  <style lang="scss" scoped>
  .el-input-number {
    width: 200px;
  }
  .box-card {
    margin-bottom: 18px;
  }
  .avatar-uploader {
    display: inline-block;
    ::v-deep .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
    .el-upload:hover {
      border-color: #409EFF;
    }
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 100px;
      height: 100px;
      line-height: 100px;
      text-align: center;
    }
    .avatar {
      width: 100px;
      height: 100px;
      display: block;
      object-fit: contain;
    }
  }
  ::v-deep .filter-list-box {
    .wrapper {
      display: inline;
      vertical-align: top;
    }
    .upload-images {
      overflow: hidden;
      background-color: #fff;
      border: 1px solid #c0ccda;
      border-radius: 6px;
      box-sizing: border-box;
      width: 100px;
      height: 100px;
      margin: 0 8px 8px 0;
      display: inline-block;
      position: relative;
      cursor: grabbing;
      .upload-image {
        width: 100%;
        height: 100%;
        display: -webkit-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        -webkit-align-items: center;
        align-items: center;
        & > img {
          width: 100%;
        }
      }
      .upload-actions {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        background-color: rgba(0,0,0,0.5);
        text-align: center;
        display: none;
        i {
          margin-left: 6px;
          margin-top: 6px;
          &:first-child {
            margin-left: 0;
          }
        }
      }
      &:hover .upload-actions {
        display: block;
      }
      .upload-actions i {
        color: #fff;
        font-size: 18px;
        cursor: pointer;
      }
    }
  }
  .notice {
    margin: 10px 0 0 0;
    color: #909399;
    font-size: 12px;
    line-height: 1.5;
  }
  </style>
  