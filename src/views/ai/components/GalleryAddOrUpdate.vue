<template>
  <el-dialog top="30px" width="1000px" :title="form.id ? $t('table.edit') : $t('table.add') " :visible.sync="visible" :close-on-click-modal="false" :close-on-press-escape="false" @closed="onClose()">
    <el-form ref="form" :model="form" :rules="rules" label-width="160px">
		<el-form-item label="分类" prop="cate_id">
		  <el-select v-model="form.cate_id" filterable placeholder="请选择分类">
		    <el-option
		        v-for="(item, index) in cateFilterOptions"
		        :key="index"
		        :label="item.label"
		        :value="item.value"
		    >
		      <span>{{ item.label }}</span>
		    </el-option>
		  </el-select>
		</el-form-item>
	  <template>
	    <el-form-item label="作品集" prop="images">
	      <div class="filter-list-box">
	        <draggable
	            v-model="form.images"
	            v-bind="dragOptions"
	            class="wrapper"
	            @start="drag = true"
	            @end="drag = false"
	        >
	          <transition-group>
	            <div v-for="(item, index) in form.images" :key="item" class="upload-images">
	              <div class="upload-image">
	                <el-image :src="item && domin + item" />
	              </div>
	              <div class="upload-actions">
	                <i class="el-icon-zoom-in" @click="onPicturePreview(item)" />
	                <i class="el-icon-delete" @click="onPictureRemove(item, index, 'images')" />
	              </div>
	            </div>
	          </transition-group>
	        </draggable>
	        <custom-upload
	            class-name="avatar-uploader"
	            accept="image/*"
	            @handleBeforeUpload="beforeAvatarUpload"
	            @handleSuccess="handleAvatarSuccesses"
	        >
	          <i slot="default" class="el-icon-plus avatar-uploader-icon" />
	        </custom-upload>
	      </div>
	    </el-form-item>
	  </template>
      <el-form-item label="标题" prop="name">
        <el-input v-model="form.name" placeholder="标题" />
      </el-form-item>
      <el-form-item label="描述" prop="desc">
        <el-input v-model="form.desc" placeholder="描述" />
      </el-form-item>
	  <el-form-item label="点赞数" prop="like_count">
	    <el-input v-model="form.like_count" placeholder="点赞数" />
	  </el-form-item>
	  <el-form-item label="作者" prop="author_id">
	    <el-select v-model="form.user_id" filterable placeholder="请选择作者">
	      <el-option
	          v-for="(item, index) in authorOptions"
	          :key="index"
	          :label="item.label"
	          :value="item.value"
	      >
	        <span>#{{ item.value }}</span>
	        <span>
	            {{ item.label }}
	          </span>
	      </el-option>
	    </el-select>
	  </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="form.sort" :min="0" placeholder="排序" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="btnLoading" @click="onFormSubmit()">
        {{ $t('table.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('table.cancel') }}
      </el-button>
    </div>

    <el-image-viewer
        v-if="imageViewer"
        :z-index="3000"
        :on-close="closeViewer"
        :url-list="imageViewerList"
    />

  </el-dialog>
</template>

<script>
import CustomUpload from '@/components/Upload/CustomUpload'
import {galleryAddOrUpdate, galleryDetails, galleryCategoryFilterList} from '@/api/ai'
import { DominKey, getToken } from '@/utils/auth'
import draggable from 'vuedraggable'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import {authorList} from "@/api/user";
export default {
  name: 'AddOrUpdate',
  components: { CustomUpload, ElImageViewer, draggable },
  data() {
    return {
      visible: false,
      btnLoading: false,
      imageViewer: false,
      imageViewerList: [],
      domin: getToken(DominKey),
      currentName: '',
	  authorOptions:[],	  // 作者
      form: {
        name: '',
        desc: '',
		like_count: 0,
        sort: 0,
        cover: '',
		cate_id: '',
		user_id: '',
		images: [],
      },
      rules: {
        images: [
          { required: false, message: '请选择封面图', trigger: ['blur', 'change'] }
        ],
        name: [
          { required: true, message: '请输入标题', trigger: ['blur', 'change'] }
        ],
        desc: [
          { required: true, message: '请输入描述', trigger: ['blur', 'change'] }
        ],
        cate_id: [
          { required: true, message: '请选择分类', trigger: ['blur', 'change'] }
        ],
      },
	  cateFilterOptions:[]
    }
  },
  computed: {
    dragOptions() {
      return {
        animation: 200,
        group: 'description',
        disabled: false,
        ghostClass: 'ghost'
      }
    }
  },
  methods: {
    init(data) {
		this.authorLists()
		if (data && data.children_category && data.children_category.length > 0) {
		  this.cateFilterOptions = []
		} else {
		  this.cateFilterLists()
		}
      this.visible = true
      if (data) {
        this.form.id = data.id
        this.getDetails()
      }
    },
    getDetails() {
      galleryDetails(this.form.id)
          .then(response => {
            this.form = response.data
          })
          .catch(() => {})
    },
	cateFilterLists() {
	  galleryCategoryFilterList({}).then(response => {
	    this.cateFilterOptions = response.data.map(v => {
	      return {
	        label: v.name,
	        value: v.id,
	      }
	    })
	  })
	},
    onFormSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id) {
            this.$confirm(`确定对[(#${this.form.id})]进行修改?`, '编辑', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
                .then(() => {
                  this.hanldaddOrUpdate()
                })
                .catch(() => {
                })
          } else {
            this.hanldaddOrUpdate()
          }
        }
      })
    },
    hanldaddOrUpdate() {
      this.form.issuer_id = this.form.issuer_id || 0
	  this.form.cover = this.form.images[0]
      const data = JSON.parse(JSON.stringify(this.form))
      this.btnLoading = true
      galleryAddOrUpdate(data)
          .then(({ msg }) => {
            this.$message.success(msg)
            this.visible = false
            this.$emit('refreshList')
          })
          .catch(() => {
            this.btnLoading = false
          })
    },
    onInfo(value) {
      this.form.content = value
    },
    onClose() {
      this.$reset()
    },
    onPicturePreview(img) {
      this.imageViewerList = [this.domin + img]
      this.imageViewer = true
    },
    onPictureRemove(img, index, type) {
      if (type === 'images') {
        this.form.images.splice(index, 1)
      }
    },
    handleRemove(file, fileList) {
      this[`${file.name.split('.')[1]}List`] = []
    },
    closeViewer() {
      this.imageViewer = false
    },
    beforeAvatarUpload3d(file, cb, refName) {
      const a = file.name.split('.')
      const isLt20M = file.size / 1024 / 1024 < 20
      if (!isLt20M) {
        this.$message.error('上传文件大小不能超过 20M')
        cb(false)
        return
      }
      this.currentName = refName
      cb(true)
    },
    handleAvatarSuccess(response, file) {
      this.form[this.currentName] = response.name
    },
    beforeAvatarUpload(file, cb, refName) {
      const type = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
      const isLt2M = file.size / 1024 / 1024 < 20
      if (!type.includes(file.type)) {
        this.$message.error('上传图片只能是 ' + type.join(',') + ' 格式!')
        cb(false)
        return
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 20M')
        cb(false)
        return
      }
      this.currentName = refName
      cb(true)
    },
	handleAvatarSuccesses(response, file) {
	  this.form.images.push(response.name)
	},
    handleError() {
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件，请删除后在上传`)
    },
	authorLists() {
	  authorList().then(response => {
	    this.authorOptions = response.data.map(v => {
	      return {
	        label: v.name,
	        value: v.id,
	      }
	    })
	  })
	},
  }
}
</script>
<style lang="scss" scoped>
.el-input-number {
  width: 200px;
}
.box-card {
  margin-bottom: 18px;
}
.avatar-uploader {
  display: inline-block;
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
    object-fit: contain;
  }
}
::v-deep .filter-list-box {
  .wrapper {
    display: inline;
    vertical-align: top;
  }
  .upload-images {
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #c0ccda;
    border-radius: 6px;
    box-sizing: border-box;
    width: 100px;
    height: 100px;
    margin: 0 8px 8px 0;
    display: inline-block;
    position: relative;
    cursor: grabbing;
    .upload-image {
      width: 100%;
      height: 100%;
      display: -webkit-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      -webkit-justify-content: center;
      justify-content: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      -webkit-align-items: center;
      align-items: center;
      & > img {
        width: 100%;
      }
    }
    .upload-actions {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background-color: rgba(0,0,0,0.5);
      text-align: center;
      display: none;
      i {
        margin-left: 6px;
        margin-top: 6px;
        &:first-child {
          margin-left: 0;
        }
      }
    }
    &:hover .upload-actions {
      display: block;
    }
    .upload-actions i {
      color: #fff;
      font-size: 18px;
      cursor: pointer;
    }
  }
}
.notice {
  margin: 10px 0 0 0;
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
}
</style>
