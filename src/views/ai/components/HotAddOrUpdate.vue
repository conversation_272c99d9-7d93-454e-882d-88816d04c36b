<template>
  <el-dialog :title="form.id ? $t('table.edit') : $t('table.add') " :visible.sync="visible" @closed="onClose()">
    <el-form ref="form" :model="form" :rules="rules" label-width="160px">
      <el-form-item label="热门问题" prop="title">
        <el-input v-model="form.title" placeholder="热门问题" clearable />
      </el-form-item>
      
      <el-form-item label="排序(越大越靠前)" prop="sort">
        <el-input-number v-model="form.sort" :min="0" :precision="0" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="btnLoading" @click="onFormSubmit()">
        {{ $t('table.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('table.cancel') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import {hotAddOrUpdate} from '@/api/ai'

export default {
  name: 'HotAddOrUpdate',
  data() {
    return {
      visible: false,
      btnLoading: false,
      form: {
        id: 0,
        title: '',
        // parent_id: '',
        sort: 0
      },

      rules: {
        title: [
          { required: true, message: '请输入问题', trigger: ['blur', 'change'] }
        ],
        sort: [
          { required: true, message: '不能为空', trigger: ['blur', 'change'] }
        ]
      },

      cateFilterOptions: []
    }
  },
  methods: {
    init(data) {
      // if (data && data.children_category && data.children_category.length > 0) {
      //   this.cateFilterOptions = []
      // } else {
      //   this.cateFilterLists()
      // }
      this.visible = true
      if (data) {
        // data.parent_id = data.parent_id || ''
        this.form = Object.assign({}, data)
      }
    },
    onFormSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.btnLoading = true
          hotAddOrUpdate(this.form)
            .then(({ msg }) => {
              this.$message.success(msg)
              this.visible = false
              this.$emit('refreshList')
            })
            .catch(() => {
              this.btnLoading = false
            })
        }
      })
    },
    onClose() {
      this.btnLoading = false
      this.$reset()
    },

    // cateFilterLists() {
    //   galleryCategoryFilterList({}).then(response => {
    //     this.cateFilterOptions = response.data.map(v => {
    //       return {
    //         label: v.name,
    //         value: v.id,
    //       }
    //     })
    //   })
    // },
  }
}
</script>
<style scoped>
.el-input-number {
  width: 200px;
}
</style>
