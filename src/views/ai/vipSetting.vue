<template>
	<div class="app-container">
		<el-form ref="form" :model="form" :rules="rules" label-width="160px">
			<el-divider content-position="left">大标题</el-divider>
			<el-form-item label="前标题" prop="title_pre">
				<el-input v-model="form.title_pre" placeholder="前标题" />
			</el-form-item>
			<el-form-item label="后标题" prop="title_suff">
				<el-input v-model="form.title_suff" placeholder="后标题" />
			</el-form-item>
			
			<el-divider content-position="left">标题下标签</el-divider>
			<el-form-item label="标签1" prop="title_sub1">
				<el-input v-model="form.title_sub1" placeholder="标签1" />
			</el-form-item>
			<el-form-item label="标签2" prop="title_sub2">
				<el-input v-model="form.title_sub2" placeholder="标签2" />
			</el-form-item>
			<el-form-item label="标签3" prop="title_sub3">
				<el-input v-model="form.title_sub3" placeholder="标签3" />
			</el-form-item>
		  
			<el-divider content-position="left">月度VIP</el-divider>
			<el-form-item label="价格" prop="vip_price1">
				<el-input v-model="form.vip_price1" placeholder="价格" />
			</el-form-item>
			<el-form-item label="价格描述" prop="vip1_sub">
				<el-input v-model="form.vip1_sub" placeholder="价格描述" />
			</el-form-item>
			<el-form-item label="优势1" prop="vip1_suff1">
				<el-input v-model="form.vip1_suff1" placeholder="优势1" />
			</el-form-item>
			<el-form-item label="优势2" prop="vip1_suff2">
				<el-input v-model="form.vip1_suff2" placeholder="优势2" />
			</el-form-item>
			<el-form-item label="优势3" prop="vip1_suff3">
				<el-input v-model="form.vip1_suff3" placeholder="优势3" />
			</el-form-item>
			<el-form-item label="优势4" prop="vip1_suff4">
				<el-input v-model="form.vip1_suff4" placeholder="优势4" />
			</el-form-item>
			
			<el-divider content-position="left">季度VIP</el-divider>
			<el-form-item label="价格" prop="vip_price2">
				<el-input v-model="form.vip_price2" placeholder="价格" />
			</el-form-item>
			<el-form-item label="价格描述" prop="vip2_sub">
				<el-input v-model="form.vip2_sub" placeholder="价格描述" />
			</el-form-item>
			<el-form-item label="优势1" prop="vip2_suff1">
				<el-input v-model="form.vip2_suff1" placeholder="优势1" />
			</el-form-item>
			<el-form-item label="优势2" prop="vip2_suff2">
				<el-input v-model="form.vip2_suff2" placeholder="优势2" />
			</el-form-item>
			<el-form-item label="优势3" prop="vip2_suff3">
				<el-input v-model="form.vip2_suff3" placeholder="优势3" />
			</el-form-item>
			<el-form-item label="优势4" prop="vip2_suff4">
				<el-input v-model="form.vip2_suff4" placeholder="优势4" />
			</el-form-item>
			
			<el-divider content-position="left">年度VIP</el-divider>
			<el-form-item label="价格" prop="vip_price3">
				<el-input v-model="form.vip_price3" placeholder="价格" />
			</el-form-item>
			<el-form-item label="价格描述" prop="vip3_sub">
				<el-input v-model="form.vip3_sub" placeholder="价格描述" />
			</el-form-item>
			<el-form-item label="优势1" prop="vip3_suff1">
				<el-input v-model="form.vip3_suff1" placeholder="优势1" />
			</el-form-item>
			<el-form-item label="优势2" prop="vip3_suff2">
				<el-input v-model="form.vip3_suff2" placeholder="优势2" />
			</el-form-item>
			<el-form-item label="优势3" prop="vip3_suff3">
				<el-input v-model="form.vip3_suff3" placeholder="优势3" />
			</el-form-item>
			<el-form-item label="优势4" prop="vip3_suff4">
				<el-input v-model="form.vip3_suff4" placeholder="优势4" />
			</el-form-item>
			
			<el-divider content-position="left">永久VIP</el-divider>
			<el-form-item label="价格" prop="vip_price4">
				<el-input v-model="form.vip_price4" placeholder="价格" />
			</el-form-item>
			<el-form-item label="价格描述" prop="vip4_sub">
				<el-input v-model="form.vip4_sub" placeholder="价格描述" />
			</el-form-item>
			<el-form-item label="优势1" prop="vip4_suff1">
				<el-input v-model="form.vip4_suff1" placeholder="优势1" />
			</el-form-item>
			<el-form-item label="优势2" prop="vip4_suff2">
				<el-input v-model="form.vip4_suff2" placeholder="优势2" />
			</el-form-item>
			<el-form-item label="优势3" prop="vip4_suff3">
				<el-input v-model="form.vip4_suff3" placeholder="优势3" />
			</el-form-item>
			<el-form-item label="优势4" prop="vip4_suff4">
				<el-input v-model="form.vip4_suff4" placeholder="优势4" />
			</el-form-item>
			
			<el-divider content-position="left">下标题</el-divider>
			<el-form-item label="前标题" prop="bottom_title_pre">
				<el-input v-model="form.bottom_title_pre" placeholder="前标题" />
			</el-form-item>
			<el-form-item label="后标题" prop="bottom_title_suff">
				<el-input v-model="form.bottom_title_suff" placeholder="后标题" />
			</el-form-item>
			
			<el-divider content-position="left">底下标签</el-divider>
			<el-form-item label="标签1" prop="title_tag1">
				<el-input v-model="form.title_tag1" placeholder="标签1" />
			</el-form-item>
			<el-form-item label="标签2" prop="title_tag2">
				<el-input v-model="form.title_tag2" placeholder="标签2" />
			</el-form-item>
			<el-form-item label="标签3" prop="title_tag3">
				<el-input v-model="form.title_tag3" placeholder="标签3" />
			</el-form-item>
			<el-form-item label="标签4" prop="title_tag4">
				<el-input v-model="form.title_tag4" placeholder="标签4" />
			</el-form-item>
			<el-form-item label="标签5" prop="title_tag5">
				<el-input v-model="form.title_tag5" placeholder="标签5" />
			</el-form-item>
			<el-form-item label="标签6" prop="title_tag6">
				<el-input v-model="form.title_tag6" placeholder="标签6" />
			</el-form-item>
		</el-form>
		<div slot="footer" class="dialog-footer">
		  <el-button type="primary" :loading="btnLoading" @click="onFormSubmit()">
			{{ $t('table.confirm') }}
		  </el-button>
		  <!-- <el-button @click="visible = false">
			{{ $t('table.cancel') }}
		  </el-button> -->
		</div>
	</div>
    
</template>

<script>
import {vipSettingAddOrUpdate, vipSettingDetails} from '@/api/ai'
import { DominKey, BindKey, getToken } from '@/utils/auth'
export default {
  name: 'aiVipSetting',
  components: { },
  data() {
    return {
      btnLoading: false,
      domin: getToken(DominKey),
      accountBind: getToken(BindKey),
      form: {
		id: 1,
        title_pre: '',
		title_suff: '',
		title_sub1: '',
		title_sub2: '',
		title_sub3: '',
		vip_price1: '',
		vip_price2: '',
		vip_price3: '',
		vip_price4: '',
		vip1_suff1: '',
		vip1_suff2: '',
		vip1_suff3: '',
		vip1_suff4: '',
		vip2_suff1: '',
		vip2_suff2: '',
		vip2_suff3: '',
		vip2_suff4: '',
		vip3_suff1: '',
		vip3_suff2: '',
		vip3_suff3: '',
		vip3_suff4: '',
		vip4_suff1: '',
		vip4_suff2: '',
		vip4_suff3: '',
		vip4_suff4: '',
		bottom_title_pre: '',
		bottom_title_suff: '',
		title_tag1: '',
		title_tag2: '',
		title_tag3: '',
		title_tag4: '',
		title_tag5: '',
		title_tag6: '',
      },
      rules: {
  //       images: [
  //         { required: true, message: '请选择封面图', trigger: ['blur', 'change'] }
  //       ],
		// link: [
		//   { required: true, message: '请上传介绍视频', trigger: ['blur', 'change'] }
		// ],
        
      },
    }
  },
  computed: {
    
  },
  created() {
    this.init()
  },
  methods: {
    init() {
		this.getDetails()
    },
    getDetails() {
      vipSettingDetails(this.form.id)
          .then(response => {
            this.form = response.data
          })
          .catch(() => {})
    },
    onFormSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id) {
            this.$confirm(`确定对[(#${this.form.id})]进行修改?`, '编辑', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
                .then(() => {
                  this.hanldaddOrUpdate()
                })
                .catch(() => {
                })
          } else {
            this.hanldaddOrUpdate()
          }
        }
      })
    },
	hanldaddOrUpdate() {
	  const data = JSON.parse(JSON.stringify(this.form))
	  this.btnLoading = true
	  vipSettingAddOrUpdate(data)
	      .then(({ msg }) => {
	        this.$message.success(msg)
	        this.visible = false
			this.btnLoading = false
			
	        this.$emit('refreshList')
	      })
	      .catch(() => {
	        this.btnLoading = false
	      })
	},
  }
}
</script>
<style lang="scss" scoped>
.el-input-number {
  width: 200px;
}
.box-card {
  margin-bottom: 18px;
}
.avatar-uploader {
  display: inline-block;
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
    object-fit: contain;
  }
}
::v-deep .filter-list-box {
  .wrapper {
    display: inline;
    vertical-align: top;
  }
  .upload-images {
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #c0ccda;
    border-radius: 6px;
    box-sizing: border-box;
    width: 100px;
    height: 100px;
    margin: 0 8px 8px 0;
    display: inline-block;
    position: relative;
    cursor: grabbing;
    .upload-image {
      width: 100%;
      height: 100%;
      display: -webkit-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      -webkit-justify-content: center;
      justify-content: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      -webkit-align-items: center;
      align-items: center;
      & > img {
        width: 100%;
      }
    }
    .upload-actions {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background-color: rgba(0,0,0,0.5);
      text-align: center;
      display: none;
      i {
        margin-left: 6px;
        margin-top: 6px;
        &:first-child {
          margin-left: 0;
        }
      }
    }
    &:hover .upload-actions {
      display: block;
    }
    .upload-actions i {
      color: #fff;
      font-size: 18px;
      cursor: pointer;
    }
  }
}
.notice {
  margin: 10px 0 0 0;
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
}
</style>
