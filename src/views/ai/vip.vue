<template>
    <div class="app-container">
      <div class="filter-container">
        <el-form :inline="true" :model="search">
          <el-form-item label="关键词">
            <el-input v-model="search.keywords" style="width: 300px;" placeholder="标题" clearable @clear="getList(1)" @keyup.enter.native="getList(1)" />
          </el-form-item>
          <el-button icon="el-icon-search" @click="getList(1)">
            {{ $t('table.search') }}
          </el-button>
          <el-button type="primary" icon="el-icon-plus" @click="onAddOrUpdate()">
            {{ $t('table.add') }}
          </el-button>
        </el-form>
      </div>
      <el-table
        v-loading="loading"
        border
        highlight-current-row
        :data="list"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="55"
          align="center"
        />
        <el-table-column
            width="160"
            label="用户名"
            align="center"
            header-align="center"
        >
          <template slot-scope="{ row }">
            {{ row.user.name }}
          </template>
        </el-table-column>
        
        <el-table-column
            width="160"
            label="是否永久会员"
            align="center"
            header-align="center"
        >
                <template slot-scope="{ row }">
            {{ row.permanent ? '是' : '否' }}
          </template>
        </el-table-column>
        
        <el-table-column
            width="140"
            prop="endtime"
            label="到期时间"
            align="center"
            header-align="center"
        />
        
        <el-table-column
            width="140"
            prop="created_at"
            label="创建时间"
            align="center"
            header-align="center"
        />
        
        <el-table-column
          label="操作"
          width="160"
          align="center"
        >
          <template slot-scope="{ row }">
            <el-button-group>
              <el-button type="primary" @click="onAddOrUpdate(row)">编辑</el-button>
              <el-button type="danger" @click="onDelete(row)">删除</el-button>
            </el-button-group>
            <br>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="pages.total > 0" :total="pages.total" :page.sync="pages.current" :limit.sync="pages.limit" @pagination="getList()" />
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update
        v-if="addOrUpdateVisible"
        ref="addOrUpdate"
        @refreshList="getList()"
      />
    </div>
  </template>
  
  <script>
  import Pagination from '@/components/Pagination'
  import { vipList,vipDelete } from '@/api/ai'
  import { getToken, DominKey } from '@/utils/auth'
  import AddOrUpdate from './components/VipAddOrUpdate'
  
  export default {
    name: 'aiVip',
    components: { AddOrUpdate, Pagination },
    data() {
      return {
        domin: getToken(DominKey),
        search: {
          keywords: '',
        },
        pages: {
          total: 0,
          limit: 20,
          current: 1
        },
        list: [],
        dateRangeValue: [],
        addOrUpdateVisible: false,
        loading: false,
      }
    },
    computed: {
     
    },
    created() {
      this.init()
    },
    methods: {
      init() {
        this.getList()
      },
      getList(page = this.pages.current, loading = true) {
        if (this.loading) return
        this.loading = loading
        vipList({ page, ...this.search, limit: this.pages.limit })
          .then(response => {
            if (response.code !== 200) return
            this.list = response.data.data
            this.pages.total = response.data.total
          })
          .catch(error => {
            this.$message.error(error.msg)
          })
          .finally(() => {
            this.loading = false
          })
      },
      onAddOrUpdate(data) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate && this.$refs.addOrUpdate.init(data)
        })
      },
      onDelete(row, index) {
        this.$confirm(
          `确定进行[删除]操作?`,
          '删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'error',
            cancelButtonClass: 'btn-custom-cancel'
          }
        )
          .then(() => {
            vipDelete(row.id)
              .then(({ msg = '删除成功' }) => {
                this.$message.success(msg)
                this.getList()
              })
              .catch(() => {})
          })
          .catch(() => {})
      }
    }
  }
  </script>
  <style lang="scss" scoped>
  ::v-deep .image-slot {
    font-size: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
  }
  .images-list {
    width: 80px;
    height: 80px;
  }
  .images-list .image-item {
    height: 100%;
    cursor: pointer;
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
  }
  ::v-deep .images-list .image-item img {
    height: auto;
  }
  .swiper-button-prev,.swiper-button-next {
    display: none;
  }
  .recommendPage:hover .swiper-button-prev,.recommendPage:hover .swiper-button-next {
    /*display: block;*/
  }
  ::v-deep .detail-content  img {
    width: 100%;
    height: auto;
  }
  ::v-deep .el-image-viewer__canvas {
    width: 80%;
    margin: 0 auto;
  }
  ::v-deep .edit-input {
    padding-right: 50px;
  }
  ::v-deep .cancel-btn {
    position: absolute;
    right: 15px;
    top: 50%;
    margin-top: -14px;
  }
  ::v-deep .edit-input .el-input-number__decrease,::v-deep .edit-input .el-input-number__increase{
    display: none;
  }
  ::v-deep .edit-input.is-controls-right .el-input__inner {
    padding: 0;
  }
  
  .recommend-page {
    width: 291px;
    margin: 0 auto;
    .swiper-container {
      user-select: none;
    }
    .swiper-button-prev,.swiper-button-next {
      display: none;
      pointer-events: auto;
      cursor: pointer;
    }
    .swiper-button-prev::after, .swiper-button-next::after {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }
    &:hover .swiper-button-prev, &:hover .swiper-button-next {
      display: flex;
    }
    .images-list {
      width: 80px;
      height: 80px;
      .image-item {
        height: 100%;
        cursor: pointer;
        display: -webkit-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        -webkit-align-items: center;
        align-items: center;
        img {
          height: auto;
        }
      }
    }
  }
  .el-tag {
    margin-right: 4px;
  }
  </style>
  