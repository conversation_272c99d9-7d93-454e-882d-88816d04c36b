<template>
  <el-dialog
    title="上传发票"
    :visible.sync="visible"
    width="700px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="upload-content">
      <!-- 开票申请信息展示 -->
      <div v-if="invoiceRecord" class="invoice-info">
        <h3 class="info-title">开票申请信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>抬头名称：</label>
              <span>{{ invoiceRecord.invoice_info.header_name }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>发票类型：</label>
              <span>{{ invoiceRecord.invoice_info.invoice_type_text }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>抬头类型：</label>
              <span>{{ invoiceRecord.invoice_info.header_type_text }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>开票金额：</label>
              <span class="amount">¥{{ invoiceRecord.invoice_amount }}</span>
            </div>
          </el-col>
          <el-col v-if="invoiceRecord.invoice_info.tax_number" :span="12">
            <div class="info-item">
              <label>税号：</label>
              <span>{{ invoiceRecord.invoice_info.tax_number }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>申请时间：</label>
              <span>{{ invoiceRecord.apply_time }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 发票号码 -->
      <div class="invoice-no-section">
        <h3 class="section-title">发票号码</h3>
        <el-input
          v-model="invoiceNo"
          placeholder="请输入发票号码（可选）"
          maxlength="50"
          show-word-limit
        />
      </div>

      <!-- 文件上传区域 -->
      <div class="upload-section">
        <h3 class="section-title">上传发票文件 <span class="required">*</span></h3>
        <el-upload
          ref="upload"
          action=""
          :http-request="customUpload"
          :file-list="fileList"
          :before-upload="beforeUpload"
          :on-success="onUploadSuccess"
          :on-error="onUploadError"
          :on-progress="onUploadProgress"
          :on-remove="onRemoveFile"
          accept=".pdf"
          :limit="1"
          :auto-upload="false"
        >
          <el-button slot="trigger" type="primary" icon="el-icon-upload">
            选择PDF文件
          </el-button>
          <div slot="tip" class="el-upload__tip">
            只能上传PDF格式文件，且不超过10MB
          </div>
        </el-upload>
        
        <!-- 上传进度 -->
        <div v-if="uploadProgress > 0 && uploadProgress < 100" class="progress-section">
          <el-progress :percentage="uploadProgress" />
        </div>
      </div>

      <!-- 备注 -->
      <div class="remark-section">
        <h3 class="section-title">备注信息</h3>
        <el-input
          v-model="remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息（可选）"
          maxlength="200"
          show-word-limit
        />
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        :loading="submitLoading"
        :disabled="!hasFile"
        @click="handleSubmit"
      >
        确认上传
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { uploadInvoice } from '@/api/recharge_order'

export default {
  name: 'InvoiceUploadDialog',
  data() {
    return {
      visible: false,
      loading: false,
      submitLoading: false,
      invoiceRecord: null,
      invoiceNo: '',
      remark: '',
      fileList: [],
      uploadProgress: 0,
      selectedFile: null
    }
  },
  computed: {
    hasFile() {
      return this.selectedFile !== null
    }
  },
  methods: {
    show(invoiceRecord) {
      this.visible = true
      this.invoiceRecord = invoiceRecord
      this.invoiceNo = invoiceRecord.invoice_no || ''
      this.remark = invoiceRecord.remark || ''
    },

    beforeUpload(file) {
      // 检查文件类型
      const isPDF = file.type === 'application/pdf'
      if (!isPDF) {
        this.$message.error('只能上传PDF格式的文件!')
        return false
      }

      // 检查文件大小
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('文件大小不能超过10MB!')
        return false
      }

      this.selectedFile = file
      return false // 阻止自动上传
    },

    customUpload(options) {
      // 这里不执行实际上传，只是为了兼容el-upload组件
      return Promise.resolve()
    },

    onUploadSuccess(response, file, fileList) {
      this.fileList = fileList
    },

    onUploadError(error, file, fileList) {
      this.$message.error('文件上传失败')
      this.fileList = fileList
    },

    onUploadProgress(event, file, fileList) {
      this.uploadProgress = Math.round(event.percent)
    },

    onRemoveFile(file, fileList) {
      this.selectedFile = null
      this.fileList = fileList
      this.uploadProgress = 0
    },

    async handleSubmit() {
      if (!this.selectedFile) {
        this.$message.warning('请先选择发票文件')
        return
      }

      if (!this.invoiceRecord) {
        this.$message.error('开票记录信息不存在')
        return
      }

      this.submitLoading = true
      try {
        // 构建FormData
        const formData = new FormData()
        formData.append('invoice_record_id', this.invoiceRecord.id)
        formData.append('invoice_file', this.selectedFile)
        
        if (this.invoiceNo) {
          formData.append('invoice_no', this.invoiceNo)
        }
        
        if (this.remark) {
          formData.append('remark', this.remark)
        }

        const response = await uploadInvoice(formData)
        if (response.code === 200) {
          this.$message.success('发票上传成功')
          this.$emit('success')
          this.handleClose()
        }
      } catch (error) {
        console.error('上传发票失败:', error)
        this.$message.error('上传发票失败')
      } finally {
        this.submitLoading = false
      }
    },

    handleClose() {
      this.visible = false
      this.invoiceRecord = null
      this.invoiceNo = ''
      this.remark = ''
      this.fileList = []
      this.selectedFile = null
      this.uploadProgress = 0
      this.loading = false
      this.submitLoading = false
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-content {
  .invoice-info {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 4px;

    .info-title {
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }

    .info-item {
      margin-bottom: 10px;
      display: flex;
      align-items: center;

      label {
        min-width: 80px;
        color: #606266;
        font-weight: 500;
      }

      span {
        color: #303133;

        &.amount {
          color: #f56c6c;
          font-weight: 600;
        }
      }
    }
  }

  .invoice-no-section,
  .upload-section,
  .remark-section {
    margin-bottom: 30px;

    .section-title {
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 14px;
      font-weight: 600;

      .required {
        color: #f56c6c;
      }
    }

    .progress-section {
      margin-top: 15px;
    }
  }
}

.dialog-footer {
  text-align: right;
}

::v-deep .el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 7px;
}
</style>
