<template>
  <el-dialog
    title="上传发票"
    :visible.sync="visible"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="upload-content">
      <!-- 开票信息展示 -->
      <div v-if="invoiceInfo" class="invoice-info">
        <h3 class="info-title">开票信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>发票类型：</label>
              <span>{{ invoiceInfo.invoice_type === 1 ? '电子普通发票' : '电子专票' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>抬头类型：</label>
              <span>{{ invoiceInfo.header_type === 1 ? '个人' : '单位' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>抬头名称：</label>
              <span>{{ invoiceInfo.header_name }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>开票金额：</label>
              <span class="amount">¥{{ invoiceInfo.invoice_amount }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 文件上传区域 -->
      <div class="upload-section">
        <h3 class="section-title">上传发票文件</h3>
        <pdf-upload
          ref="pdfUpload"
          :file-list="fileList"
          @handleSuccess="handleUploadSuccess"
          @handleRemove="handleUploadRemove"
          @elProgress="handleProgress"
        >
          <el-button type="primary" icon="el-icon-upload">
            选择PDF文件
          </el-button>
        </pdf-upload>
        
        <!-- 上传进度 -->
        <div v-if="uploadProgress > 0 && uploadProgress < 1" class="progress-section">
          <el-progress :percentage="Math.round(uploadProgress * 100)" />
        </div>
      </div>

      <!-- 备注 -->
      <div class="remark-section">
        <h3 class="section-title">备注信息</h3>
        <el-input
          v-model="remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息（可选）"
          maxlength="200"
          show-word-limit
        />
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        :loading="submitLoading"
        :disabled="!uploadedFile"
        @click="handleSubmit"
      >
        确认上传
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import PdfUpload from '@/components/Upload/PdfUpload'
import { getInvoiceDetail, uploadInvoice } from '@/api/recharge_order'

export default {
  name: 'InvoiceUploadDialog',
  components: {
    PdfUpload
  },
  data() {
    return {
      visible: false,
      loading: false,
      submitLoading: false,
      orderId: null,
      invoiceInfo: null,
      fileList: [],
      uploadedFile: null,
      uploadProgress: 0,
      remark: ''
    }
  },
  methods: {
    show(orderId) {
      this.visible = true
      this.orderId = orderId
      this.getInvoiceInfo()
    },

    async getInvoiceInfo() {
      if (!this.orderId) return

      this.loading = true
      try {
        const response = await getInvoiceDetail(this.orderId)
        if (response.code === 200) {
          this.invoiceInfo = response.data
        }
      } catch (error) {
        console.error('获取开票信息失败:', error)
        this.$message.error('获取开票信息失败')
      } finally {
        this.loading = false
      }
    },

    handleUploadSuccess(res, file, fileList) {
      this.uploadedFile = {
        url: res.url,
        name: res.name || file.name
      }
      this.fileList = fileList
      this.$message.success('文件上传成功')
    },

    handleUploadRemove(file, fileList) {
      this.uploadedFile = null
      this.fileList = fileList
    },

    handleProgress(progress) {
      this.uploadProgress = progress
    },

    async handleSubmit() {
      if (!this.uploadedFile) {
        this.$message.warning('请先上传发票文件')
        return
      }

      this.submitLoading = true
      try {
        const data = {
          invoice_file_url: this.uploadedFile.url,
          invoice_file_name: this.uploadedFile.name,
          remark: this.remark
        }

        const response = await uploadInvoice(this.orderId, data)
        if (response.code === 200) {
          this.$message.success('发票上传成功')
          this.$emit('success')
          this.handleClose()
        }
      } catch (error) {
        console.error('上传发票失败:', error)
        this.$message.error('上传发票失败')
      } finally {
        this.submitLoading = false
      }
    },

    handleClose() {
      this.visible = false
      this.orderId = null
      this.invoiceInfo = null
      this.fileList = []
      this.uploadedFile = null
      this.uploadProgress = 0
      this.remark = ''
      this.loading = false
      this.submitLoading = false
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-content {
  .invoice-info {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 4px;

    .info-title {
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }

    .info-item {
      margin-bottom: 10px;
      display: flex;
      align-items: center;

      label {
        min-width: 80px;
        color: #606266;
        font-weight: 500;
      }

      span {
        color: #303133;

        &.amount {
          color: #f56c6c;
          font-weight: 600;
        }
      }
    }
  }

  .upload-section,
  .remark-section {
    margin-bottom: 30px;

    .section-title {
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 14px;
      font-weight: 600;
    }

    .progress-section {
      margin-top: 15px;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
