<template>
  <el-dialog
    title="开票记录详情"
    :visible.sync="visible"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="invoice-records">
      <div v-if="orderData" class="order-info">
        <h3 class="section-title">订单信息</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>订单号：</label>
              <span>{{ orderData.order_no }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>用户：</label>
              <span>{{ orderData.user.name }} ({{ orderData.user.phone }})</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>订单金额：</label>
              <span class="amount">¥{{ orderData.order_amount }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>支付时间：</label>
              <span>{{ orderData.pay_time }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>开票次数：</label>
              <span class="count">{{ orderData.invoice_count }}次</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>开票状态：</label>
              <el-tag :type="getStatusType(orderData.invoice_status)" size="mini">
                {{ getStatusText(orderData.invoice_status) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </div>

      <div v-if="invoiceRecords && invoiceRecords.length > 0" class="records-section">
        <h3 class="section-title">开票记录</h3>
        <div class="records-list">
          <div
            v-for="(record, index) in invoiceRecords"
            :key="record.id"
            class="record-item"
          >
            <div class="record-header">
              <span class="record-title">第{{ index + 1 }}次开票申请</span>
              <el-tag :type="getRecordStatusType(record.status)" size="mini">
                {{ record.status_text }}
              </el-tag>
            </div>
            
            <div class="record-content">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="info-item">
                    <label>抬头名称：</label>
                    <span>{{ record.invoice_info.header_name }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <label>发票类型：</label>
                    <span>{{ record.invoice_info.invoice_type_text }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <label>抬头类型：</label>
                    <span>{{ record.invoice_info.header_type_text }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <label>开票金额：</label>
                    <span class="amount">¥{{ record.invoice_amount }}</span>
                  </div>
                </el-col>
                <el-col v-if="record.invoice_info.tax_number" :span="12">
                  <div class="info-item">
                    <label>税号：</label>
                    <span>{{ record.invoice_info.tax_number }}</span>
                  </div>
                </el-col>
                <el-col v-if="record.invoice_no" :span="12">
                  <div class="info-item">
                    <label>发票号码：</label>
                    <span>{{ record.invoice_no }}</span>
                  </div>
                </el-col>
              </el-row>

              <!-- 专票详细信息 -->
              <div v-if="record.invoice_info.registered_address" class="company-detail">
                <h4>单位详细信息</h4>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="info-item">
                      <label>注册地址：</label>
                      <span>{{ record.invoice_info.registered_address }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info-item">
                      <label>注册电话：</label>
                      <span>{{ record.invoice_info.registered_phone }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info-item">
                      <label>开户银行：</label>
                      <span>{{ record.invoice_info.bank_name }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info-item">
                      <label>银行账号：</label>
                      <span>{{ record.invoice_info.bank_account }}</span>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <div class="record-footer">
                <div class="time-info">
                  <span>申请时间：{{ record.apply_time }}</span>
                  <span v-if="record.upload_time">上传时间：{{ record.upload_time }}</span>
                </div>
                <div class="actions">
                  <el-button
                    v-if="record.invoice_file_url"
                    type="text"
                    icon="el-icon-download"
                    @click="downloadInvoice(record)"
                  >
                    下载发票
                  </el-button>
                </div>
              </div>

              <div v-if="record.remark" class="record-remark">
                <label>备注：</label>
                <span>{{ record.remark }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="no-data">
        <i class="el-icon-document"></i>
        <p>暂无开票记录</p>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getInvoiceRecords } from '@/api/recharge_order'

export default {
  name: 'InvoiceRecordsDialog',
  data() {
    return {
      visible: false,
      loading: false,
      orderData: null,
      invoiceRecords: [],
      orderId: null
    }
  },
  methods: {
    show(orderId) {
      this.visible = true
      this.orderId = orderId
      this.getInvoiceRecords()
    },

    async getInvoiceRecords() {
      if (!this.orderId) return

      this.loading = true
      try {
        const response = await getInvoiceRecords(this.orderId)
        if (response.code === 200) {
          this.orderData = response.data.recharge_order
          this.invoiceRecords = response.data.invoice_records
        }
      } catch (error) {
        console.error('获取开票记录失败:', error)
        this.$message.error('获取开票记录失败')
      } finally {
        this.loading = false
      }
    },

    getStatusType(status) {
      const statusMap = {
        0: 'info',    // 未开票
        1: 'warning', // 已申请
        2: 'success'  // 已完成
      }
      return statusMap[status] || 'info'
    },

    getStatusText(status) {
      const statusMap = {
        0: '未开票',
        1: '已申请',
        2: '已完成'
      }
      return statusMap[status] || '未知'
    },

    getRecordStatusType(status) {
      const statusMap = {
        1: 'warning', // 待处理
        2: 'success', // 已上传
        3: 'info'     // 重开中
      }
      return statusMap[status] || 'info'
    },

    downloadInvoice(record) {
      if (record.invoice_file_url) {
        // 直接打开文件URL进行下载
        const link = document.createElement('a')
        link.href = record.invoice_file_url
        link.download = `发票_${record.invoice_no || record.id}.pdf`
        link.target = '_blank'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        this.$message.success('发票下载成功')
      } else {
        this.$message.warning('发票文件不存在')
      }
    },

    handleClose() {
      this.visible = false
      this.orderData = null
      this.invoiceRecords = []
      this.orderId = null
    }
  }
}
</script>

<style lang="scss" scoped>
.invoice-records {
  min-height: 400px;

  .order-info {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 4px;
  }

  .section-title {
    margin: 0 0 20px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #e4e7ed;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .info-item {
    margin-bottom: 15px;
    display: flex;
    align-items: center;

    label {
      min-width: 80px;
      color: #606266;
      font-weight: 500;
    }

    span {
      color: #303133;

      &.amount {
        color: #f56c6c;
        font-weight: 600;
      }

      &.count {
        color: #409eff;
        font-weight: 600;
      }
    }
  }

  .records-section {
    .records-list {
      .record-item {
        margin-bottom: 20px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        overflow: hidden;

        .record-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 15px 20px;
          background-color: #f5f7fa;
          border-bottom: 1px solid #e4e7ed;

          .record-title {
            font-weight: 600;
            color: #303133;
          }
        }

        .record-content {
          padding: 20px;

          .company-detail {
            margin-top: 20px;
            padding: 15px;
            background-color: #fafafa;
            border-radius: 4px;

            h4 {
              margin: 0 0 15px 0;
              color: #606266;
              font-size: 14px;
            }
          }

          .record-footer {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;

            .time-info {
              span {
                margin-right: 20px;
                color: #909399;
                font-size: 12px;
              }
            }
          }

          .record-remark {
            margin-top: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            font-size: 12px;
            color: #606266;

            label {
              font-weight: 500;
            }
          }
        }
      }
    }
  }

  .no-data {
    text-align: center;
    padding: 60px 0;
    color: #909399;

    .el-icon-document {
      font-size: 48px;
      margin-bottom: 16px;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
