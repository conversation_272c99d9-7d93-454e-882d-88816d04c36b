<template>
  <el-dialog
    title="开票详情信息"
    :visible.sync="visible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="invoice-detail">
      <div v-if="invoiceData" class="detail-content">
        <!-- 基本信息 -->
        <div class="section">
          <h3 class="section-title">开票基本信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>发票类型：</label>
                <span>{{ invoiceData.invoice_type === 1 ? '电子普通发票' : '电子专票' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>抬头类型：</label>
                <span>{{ invoiceData.header_type === 1 ? '个人' : '单位' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>抬头名称：</label>
                <span>{{ invoiceData.header_name }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>发票内容：</label>
                <span>{{ invoiceData.invoice_content || '技术咨询服务' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>开票金额：</label>
                <span class="amount">¥{{ invoiceData.invoice_amount }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>开票次数：</label>
                <span class="count">{{ invoiceData.invoice_count }}次</span>
              </div>
            </el-col>
          </el-row>

          <!-- 单位信息（如果是单位抬头） -->
          <div v-if="invoiceData.header_type === 2" class="company-info">
            <h4>单位信息</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-item">
                  <label>单位税号：</label>
                  <span>{{ invoiceData.tax_number }}</span>
                </div>
              </el-col>
              <el-col v-if="invoiceData.invoice_type === 2" :span="12">
                <div class="info-item">
                  <label>注册地址：</label>
                  <span>{{ invoiceData.registered_address }}</span>
                </div>
              </el-col>
              <el-col v-if="invoiceData.invoice_type === 2" :span="12">
                <div class="info-item">
                  <label>注册电话：</label>
                  <span>{{ invoiceData.registered_phone }}</span>
                </div>
              </el-col>
              <el-col v-if="invoiceData.invoice_type === 2" :span="12">
                <div class="info-item">
                  <label>开户银行：</label>
                  <span>{{ invoiceData.bank_name }}</span>
                </div>
              </el-col>
              <el-col v-if="invoiceData.invoice_type === 2" :span="12">
                <div class="info-item">
                  <label>银行账号：</label>
                  <span>{{ invoiceData.bank_account }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 开票历史 -->
        <div class="section">
          <h3 class="section-title">开票历史记录</h3>
          <el-timeline>
            <el-timeline-item
              v-for="(record, index) in invoiceData.history"
              :key="index"
              :timestamp="record.created_at"
              placement="top"
            >
              <div class="timeline-content">
                <div class="timeline-header">
                  <span class="timeline-title">第{{ index + 1 }}次开票</span>
                  <el-tag
                    :type="record.status === 1 ? 'success' : 'warning'"
                    size="mini"
                  >
                    {{ record.status === 1 ? '已开票' : '待开票' }}
                  </el-tag>
                </div>
                <div v-if="record.invoice_file_url" class="timeline-file">
                  <el-button
                    type="text"
                    icon="el-icon-download"
                    @click="downloadInvoice(record)"
                  >
                    下载发票
                  </el-button>
                </div>
                <div v-if="record.remark" class="timeline-remark">
                  备注：{{ record.remark }}
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>

      <div v-else class="no-data">
        <i class="el-icon-document"></i>
        <p>暂无开票信息</p>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getInvoiceDetail, downloadInvoice } from '@/api/recharge_order'

export default {
  name: 'InvoiceDetailDialog',
  data() {
    return {
      visible: false,
      loading: false,
      invoiceData: null,
      orderId: null
    }
  },
  methods: {
    show(orderId) {
      this.visible = true
      this.orderId = orderId
      this.getInvoiceDetail()
    },

    async getInvoiceDetail() {
      if (!this.orderId) return

      this.loading = true
      try {
        const response = await getInvoiceDetail(this.orderId)
        if (response.code === 200) {
          this.invoiceData = response.data
        }
      } catch (error) {
        console.error('获取开票详情失败:', error)
        this.$message.error('获取开票详情失败')
      } finally {
        this.loading = false
      }
    },

    async downloadInvoice(record) {
      try {
        const response = await downloadInvoice(this.orderId, record.id)
        
        // 创建下载链接
        const blob = new Blob([response], { type: 'application/pdf' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `发票_${record.id}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        
        this.$message.success('发票下载成功')
      } catch (error) {
        console.error('下载发票失败:', error)
        this.$message.error('下载发票失败')
      }
    },

    handleClose() {
      this.visible = false
      this.invoiceData = null
      this.orderId = null
    }
  }
}
</script>

<style lang="scss" scoped>
.invoice-detail {
  min-height: 300px;

  .detail-content {
    .section {
      margin-bottom: 30px;

      .section-title {
        margin: 0 0 20px 0;
        padding-bottom: 10px;
        border-bottom: 1px solid #e4e7ed;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .info-item {
        margin-bottom: 15px;
        display: flex;
        align-items: center;

        label {
          min-width: 80px;
          color: #606266;
          font-weight: 500;
        }

        span {
          color: #303133;

          &.amount {
            color: #f56c6c;
            font-weight: 600;
          }

          &.count {
            color: #409eff;
            font-weight: 600;
          }
        }
      }

      .company-info {
        margin-top: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 4px;

        h4 {
          margin: 0 0 15px 0;
          color: #606266;
          font-size: 14px;
        }
      }
    }

    .timeline-content {
      .timeline-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;

        .timeline-title {
          font-weight: 600;
          color: #303133;
        }
      }

      .timeline-file {
        margin-bottom: 5px;
      }

      .timeline-remark {
        color: #909399;
        font-size: 12px;
      }
    }
  }

  .no-data {
    text-align: center;
    padding: 60px 0;
    color: #909399;

    .el-icon-document {
      font-size: 48px;
      margin-bottom: 16px;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
