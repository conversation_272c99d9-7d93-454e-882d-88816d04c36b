<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" :model="search">
        <el-form-item label="关键词">
          <el-input v-model="search.keywords" style="width: 300px;" placeholder="课程名称" clearable @clear="getList(1)" @keyup.enter.native="getList(1)" />
        </el-form-item>
        <el-form-item label="是否发布">
          <el-select v-model="search.is_publish" clearable @change="getList(1)">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类筛选">
          <el-select v-model="search.category_id" clearable placeholder="请选择分类" @change="getList(1)">
            <el-option v-for="item in categoryOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-button icon="el-icon-search" @click="getList(1)">
          {{ $t('table.search') }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" @click="onAddOrUpdate()">
          {{ $t('table.add') }}
        </el-button>
        <el-button type="warning" icon="el-icon-discount" @click="onDiscountConfig()">
          打折配置
        </el-button>
      </el-form>
    </div>
    <el-table
      v-loading="loading"
      border
      highlight-current-row
      :data="list"
    >
      <el-table-column
        prop="id"
        label="ID"
        width="55"
        align="center"
      />
      <el-table-column
          width="120"
          label="封面图"
          header-align="center"
      >
        <template slot-scope="{ row }">
          <div class="info-wrapper">
            <el-image
                class="image-item"
                :src="row.images && domin + row.images"
                :preview-src-list="[domin + row.images]"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline" />
              </div>
            </el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column
          min-width="140"
          label="名称"
          header-align="center"
      >
        <template slot-scope="{ row }">
          {{ row.name }}
        </template>
      </el-table-column>
      <el-table-column min-width="140" label="标签" header-align="center">
        <template slot-scope="{ row }">
          <el-tag v-for="(item , i) in row.tag_list_info" :key="i">
              {{ item.tag}}
            </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="推荐人" width="100" header-align="center">
        <template slot-scope="{ row }">
          {{row.suggest_user ? row.suggest_user.name : '平台' }}
        </template>
      </el-table-column>
      <el-table-column
        label="基础信息"
        min-width="280"
        header-align="center"
      >
        <template slot-scope="{ row }">
          <div>
            价格(光子)：{{ row.price || 0}}
          </div>
          <div>
            划线价格(光子)：{{ row.market_price || 0 }}
          </div>
<!--          <div>-->
<!--            作者：{{ row.author_info ? row.author_info.name : '-' }}-->
<!--          </div>-->
          <div>
            分类：<el-tag>
                {{
                row.cate
                  ? row.cate.parent_category
                    ? row.cate.parent_category.name + " / " + row.cate.name
                    : row.cate.name
                  : "-"
              }}
              </el-tag>
          </div>
          <div>
            深造ID：<span style="color: #b419be">{{ row.sz_course_id || '-'}}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column width="70" label="发布状态" align="center">
        <template slot-scope="{ row }">
          {{ row.is_publish ? '已发布':'未发布' }}
        </template>
      </el-table-column>
      <el-table-column
          label="购买"
          min-width="80"
          header-align="center"
      >
        <template slot-scope="{ row }">
<!--          <div>-->
<!--            光粒抵扣：{{ row.is_integral ? '是':'否' }}-->
<!--          </div>-->
<!--          <div v-show="row.is_integral">-->
<!--            光粒抵扣数量：{{ row.integral_num>0?row.integral_num:'不限' }}-->
<!--          </div>-->
<!--          <div>-->
<!--            赠送光粒：{{ row.give_integral || 0}}-->
<!--          </div>-->
          <div>
            购买量：{{ row.num || 0}}
          </div>
        </template>
      </el-table-column>
      <el-table-column
          width="85"
          prop="created_at"
          label="创建时间"
          header-align="center"
      />
      <el-table-column
        label="操作"
        width="250"
        align="center"
        fixed="right"
      >
        <template slot-scope="{ row, $index }">
          <el-button-group>
            <el-button type="primary" @click="onCourseDetail(row)">详情</el-button>
            <el-button type="primary" plain @click="onComment(row)">评论</el-button>
            <el-button v-if="![4].includes(row.type)" type="primary" @click="onAddOrUpdate(row)">编辑</el-button>
            <el-button type="danger" @click="onDelete(row, $index)">删除</el-button>
          </el-button-group>
          <br>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="pages.total > 0" :total="pages.total" :page.sync="pages.current" :limit.sync="pages.limit" @pagination="getList()" />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshList="getList()"
    />
<!--课程详情-->
    <course-detail
        v-if="courseDetailVisible"
        ref="courseDetail"
        @refreshList="getList()"
    />
    <!-- 弹窗, 评论 -->
    <comment
        v-if="commentVisible"
        ref="comment"
    />
    <!-- 打折配置弹窗 -->
    <discount-config
        v-if="discountConfigVisible"
        ref="discountConfig"
        @refreshList="getList()"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { dataList, courseDelete } from '@/api/course'
import { cateFilterList } from '@/api/category'
import { getToken, DominKey } from '@/utils/auth'
import AddOrUpdate from './components/AddOrUpdate'
import { pickerOptions } from '@/utils/explain'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import 'swiper/swiper-bundle.css'
import CourseDetail from "./components/CourseDetail";
import Comment from '../comment/Comment'
import DiscountConfig from '@/components/DiscountConfig'

export default {
  name: 'CourseList',
  components: {CourseDetail, AddOrUpdate, Pagination, Comment, Swiper, SwiperSlide, DiscountConfig },
  data() {
    return {
      swiperOption: {
        slidesPerView: 3,
        spaceBetween: 10,
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        }
      },
      domin: getToken(DominKey),
      search: {
        keywords: '',
        is_publish: '',
        category_id: '',
      },
      pages: {
        total: 0,
        limit: 20,
        current: 1
      },
      list: [],
      dateRangeValue: [],
      addOrUpdateVisible: false,
      courseDetailVisible: false,
      commentVisible: false,
      discountConfigVisible: false,
      loading: false,
      pickerOptions,
      statusOptions:[
        { label: '全部', value: '' },
        { label: '未发布', value: 0 },
        { label: '已发布', value: 1 }
      ],
      categoryOptions: []
    }
  },
  computed: {
    swiper() {
      return function(v = 0) {
        return this.$refs[`mySwiper${v}`].$swiper
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.getCategoryList()
      this.getList()
    },
    getCategoryList() {
      cateFilterList()
        .then(response => {
          if (response.code === 200) {
            this.categoryOptions = response.data || []
          }
        })
        .catch(() => {
          // 错误处理
        })
    },
    getList(page = this.pages.current, loading = true) {
      if (this.loading) return
      this.loading = loading
      dataList({ page, ...this.search, limit: this.pages.limit })
        .then(response => {
          if (response.code !== 200) return
          this.list = response.data.data
          this.pages.total = response.data.total
        })
        .catch(error => {
          this.$message.error(error.msg)
        })
        .finally(() => {
          this.loading = false
        })
    },
    onAddOrUpdate(data) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate && this.$refs.addOrUpdate.init(data)
      })
    },
    onCourseDetail(data) {
      console.log(11);
      this.courseDetailVisible = true
      this.$nextTick(() => {
        this.$refs.courseDetail && this.$refs.courseDetail.init(data)
      })
    },
    prev(index) {
      this.swiper(index).slidePrev()
    },
    next(index) {
      this.swiper(index).slideNext()
    },
    onComment(data) {
      this.commentVisible = true
      data.target_type = 'task';
      this.$nextTick(() => {
        this.$refs.comment && this.$refs.comment.init(data)
      })
    },
    onDelete(row, index) {
      this.$confirm(
        `确定进行[删除]操作?`,
        '删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'error',
          cancelButtonClass: 'btn-custom-cancel'
        }
      )
        .then(() => {
          courseDelete(row.id)
            .then(({ msg = '删除成功' }) => {
              this.$message.success(msg)
              this.getList()
            })
            .catch(() => {})
        })
        .catch(() => {})
    },
    onDiscountConfig() {
      this.discountConfigVisible = true
      this.$nextTick(() => {
        this.$refs.discountConfig && this.$refs.discountConfig.init()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .image-slot {
  font-size: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
}
.images-list {
  width: 80px;
  height: 80px;
}
.images-list .image-item {
  height: 100%;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
}
::v-deep .images-list .image-item img {
  height: auto;
}
.swiper-button-prev,.swiper-button-next {
  display: none;
}
.recommendPage:hover .swiper-button-prev,.recommendPage:hover .swiper-button-next {
  /*display: block;*/
}
::v-deep .detail-content  img {
  width: 100%;
  height: auto;
}
::v-deep .el-image-viewer__canvas {
  width: 80%;
  margin: 0 auto;
}
::v-deep .edit-input {
  padding-right: 50px;
}
::v-deep .cancel-btn {
  position: absolute;
  right: 15px;
  top: 50%;
  margin-top: -14px;
}
::v-deep .edit-input .el-input-number__decrease,::v-deep .edit-input .el-input-number__increase{
  display: none;
}
::v-deep .edit-input.is-controls-right .el-input__inner {
  padding: 0;
}

.recommend-page {
  width: 291px;
  margin: 0 auto;
  .swiper-container {
    user-select: none;
  }
  .swiper-button-prev,.swiper-button-next {
    display: none;
    pointer-events: auto;
    cursor: pointer;
  }
  .swiper-button-prev::after, .swiper-button-next::after {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }
  &:hover .swiper-button-prev, &:hover .swiper-button-next {
    display: flex;
  }
  .images-list {
    width: 80px;
    height: 80px;
    .image-item {
      height: 100%;
      cursor: pointer;
      display: -webkit-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      -webkit-justify-content: center;
      justify-content: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      -webkit-align-items: center;
      align-items: center;
      img {
        height: auto;
      }
    }
  }
}
.el-tag {
  margin-right: 4px;
}
</style>
