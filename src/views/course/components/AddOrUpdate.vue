<template>
  <el-dialog
    top="30px"
    width="1000px"
    :title="form.id ? $t('table.edit') : $t('table.add')"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @closed="onClose()"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="160px">
      <template>
        <el-form-item label="封面图" prop="images">
          <custom-upload
            class-name="avatar-uploader"
            ref-name="images"
            @handleBeforeUpload="beforeAvatarUpload"
            @handleSuccess="handleAvatarSuccess"
          >
            <img v-if="form.images" :src="domin + form.images" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </custom-upload>
          <div class="notice">注意：建议封面图尺寸 750*750px</div>
        </el-form-item>
      </template>
      <el-form-item label="课程名称" prop="name">
        <el-input v-model="form.name" placeholder="课程名称" />
      </el-form-item>
      <!--      <el-form-item label="作者" prop="author">-->
      <!--        <el-input v-model="form.author" placeholder="作者" />-->
      <!--      </el-form-item>-->

      <el-form-item label="标签" prop="tag">
        <el-select
          v-model="form.tag"
          remote
          multiple
          filterable
          placeholder="请选择标签"
        >
          <el-option
            v-for="(item, index) in tagList"
            :key="index"
            :label="item.tag"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="价格" prop="price">
        <el-input-number
          v-model="form.price"
          :precision="2"
          :min="0"
          placeholder="价格"
        />
        <span style="color: red">光子</span>
      </el-form-item>
      <el-form-item label="划线价格" prop="market_price">
        <el-input-number
          v-model="form.market_price"
          :precision="2"
          :min="0"
          placeholder="划线价格"
        />
        <span style="color: red">光子</span>
      </el-form-item>
      <!--      <el-form-item label="是否抵扣" prop="is_integral">-->
      <!--        <el-switch-->
      <!--            v-model="form.is_integral"-->
      <!--            :inactive-value="0"-->
      <!--            :active-value="1"-->
      <!--        />-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="抵扣光粒" prop="integral_num" v-show="form.is_integral==1">-->
      <!--        <el-input-number v-model="form.integral_num" :precision="2" :min="0" placeholder="抵扣光粒" />-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="赠送光粒" prop="give_integral">-->
      <!--        <el-input-number v-model="form.give_integral" :precision="2" :min="0" placeholder="赠送光粒" />-->
      <!--      </el-form-item>-->
      <el-form-item label="是否发布" prop="is_publish">
        <el-switch
          v-model="form.is_publish"
          :inactive-value="0"
          :active-value="1"
        />
      </el-form-item>
      <el-form-item label="推荐人" prop="author_id">
        <el-select v-model="form.user_id" filterable placeholder="请选择推荐人">
          <el-option
            v-for="(item, index) in authorOptions"
            :key="index"
            :label="item.label"
            :value="item.value"
          >
            <span>#{{ item.value }}</span>
            <span>
              {{ item.label }}
            </span>
          </el-option>
        </el-select>
      </el-form-item>
      <!--      <el-form-item label="作者" prop="author_id">-->
      <!--        <el-select v-model="form.author_id" filterable placeholder="请选择作者">-->
      <!--          <el-option-->
      <!--              v-for="(item, index) in authorOptions"-->
      <!--              :key="index"-->
      <!--              :label="item.label"-->
      <!--              :value="item.value"-->
      <!--          >-->
      <!--            <span>#{{ item.value }}</span>-->
      <!--            <span>-->
      <!--                {{ item.label }}-->
      <!--              </span>-->
      <!--          </el-option>-->
      <!--        </el-select>-->
      <!--      </el-form-item>-->
      <el-form-item label="分类" prop="cate_id">
        <el-select v-model="form.cate_id" filterable placeholder="请选择分类">
          <el-option
            v-for="(item, index) in cateOptions"
            :key="index"
            :label="item.label"
            :value="item.value"
          >
            <span>{{ item.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number
          v-model="form.sort"
          :precision="4"
          :min="0"
          placeholder="排序"
        />
      </el-form-item>
      <el-form-item label="课程简介" prop="explain">
        <el-input
          v-model="form.explain"
          type="textarea"
          :rows="8"
          placeholder="课程简介"
          clearable
        />
      </el-form-item>
      <el-form-item label="课程介绍" prop="intro">
        <el-link type="primary" :underline="false" @click="onTinymce(form)"
          >点击编辑</el-link
        >
      </el-form-item>
      <el-form-item label="深造ID" prop="sz_course_id">
        <el-input v-model="form.sz_course_id" placeholder="深造ID" />
      </el-form-item>
      <el-form-item label="是否推送" prop="send">
        <el-switch v-model="form.send" :inactive-value="0" :active-value="1" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="btnLoading" @click="onFormSubmit()">
        {{ $t("table.confirm") }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t("table.cancel") }}
      </el-button>
    </div>

    <el-image-viewer
      v-if="imageViewer"
      :z-index="3000"
      :on-close="closeViewer"
      :url-list="imageViewerList"
    />

    <edit-tinymce v-if="editTinymceVisible" ref="editTinymce" @info="onInfo" />
  </el-dialog>
</template>

<script>
import CustomUpload from "@/components/Upload/CustomUpload";
import { addOrUpdate, details, getTagList } from "@/api/course";
import { DominKey, getToken } from "@/utils/auth";
import EditTinymce from "./EditTinymce";
import draggable from "vuedraggable";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import { authorList, cateList } from "@/api/user";
export default {
  name: "AddOrUpdate",
  components: { CustomUpload, EditTinymce, ElImageViewer, draggable },
  data() {
    return {
      tagloading:false,
      tagList:[],
      visible: false,
      btnLoading: false,
      editTinymceVisible: false,
      imageViewer: false,
      imageViewerList: [],
      domin: getToken(DominKey),
      currentName: "",
      // 作者
      authorOptions: [],
      cateOptions: [],
      form: {
        name: "",
        author: "",
        intro: "",
        explain: "",
        images: "",
        sz_course_id: "",
        is_publish: 0,
        is_integral: 0,
        integral_num: 0,
        give_integral: 0,
        price: 0,
        send: 0,
        market_price: 0,
      },
      rules: {
        is_publish: [
          {
            required: true,
            message: "请选择是否发布",
            trigger: ["blur", "change"],
          },
        ],
        // images: [
        //   { required: true, message: '请选择封面图', trigger: ['blur', 'change'] }
        // ],
        name: [
          {
            required: true,
            message: "请输入课程名称",
            trigger: ["blur", "change"],
          },
        ],
        author: [
          {
            required: true,
            message: "请输入创作者",
            trigger: ["blur", "change"],
          },
        ],
        intro: [
          {
            required: true,
            message: "请输入课程介绍",
            trigger: ["blur", "change"],
          },
        ],
        explain: [
          {
            required: true,
            message: "请输入课程说明",
            trigger: ["blur", "change"],
          },
        ],
        price: [
          {
            required: true,
            message: "请输入价格",
            trigger: ["blur", "change"],
          },
        ],
        market_price: [
          {
            required: true,
            message: "请输入划线价格",
            trigger: ["blur", "change"],
          },
        ],
      },
    };
  },
  computed: {
    dragOptions() {
      return {
        animation: 200,
        group: "description",
        disabled: false,
        ghostClass: "ghost",
      };
    },
  },
  methods: {
    init(data) {
      this.visible = true;
      this.authorLists();
      this.cateLists();
      if (data) {
        this.form.id = data.id;
        this.getDetails();
      }
      this.fungetTagList();
    },
   
    fungetTagList(keyword = '') {
      getTagList({ type:'all', keywords:keyword}).then((res) => {
        this.tagList = res.data;
        this.tagloading = false;
        console.log(res, this.tagList);

      });
    },
    getDetails() {
      details(this.form.id)
        .then((response) => {
          this.form = response.data;
        })
        .catch(() => {});
    },
    onFormSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id) {
            this.$confirm(`确定对[(#${this.form.id})]进行修改?`, "编辑", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {
                this.hanldaddOrUpdate();
              })
              .catch(() => {});
          } else {
            this.hanldaddOrUpdate();
          }
        }
      });
    },
    hanldaddOrUpdate() {
      this.form.issuer_id = this.form.issuer_id || 0;
      const data = JSON.parse(JSON.stringify(this.form));
      this.btnLoading = true;
      addOrUpdate(data)
        .then(({ msg }) => {
          this.$message.success(msg);
          this.visible = false;
          this.$emit("refreshList");
        })
        .catch(() => {
          this.btnLoading = false;
        });
    },
    onTinymce(data) {
      this.editTinymceVisible = true;
      this.$nextTick(() => {
        this.$refs.editTinymce && this.$refs.editTinymce.init(data);
      });
    },
    onInfo(value) {
      this.form.intro = value;
    },
    onClose() {
      this.$reset();
    },
    closeViewer() {
      this.imageViewer = false;
    },
    beforeAvatarUpload3d(file, cb, refName) {
      const a = file.name.split(".");
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M");
        cb(false);
        return;
      }
      this.currentName = refName;
      cb(true);
    },
    elProgress(p) {},
    handleAvatarSuccess(response, file) {
      this.form[this.currentName] = response.name;
    },
    beforeAvatarUpload(file, cb, refName) {
      const type = ["image/jpeg", "image/jpg", "image/png", "image/gif"];
      const isLt2M = file.size / 1024 / 1024 < 20;
      if (!type.includes(file.type)) {
        this.$message.error("上传图片只能是 " + type.join(",") + " 格式!");
        cb(false);
        return;
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 20M");
        cb(false);
        return;
      }
      this.currentName = refName;
      cb(true);
    },
    authorLists() {
      authorList().then((response) => {
        this.authorOptions = response.data.map((v) => {
          return {
            label: v.name,
            value: v.id,
          };
        });
      });
    },
    cateLists() {
      cateList().then((response) => {
        this.cateOptions = response.data.map((v) => {
          return {
            label: v.name,
            value: v.id,
          };
        });
      });
    },
    handleError() {},
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件，请删除后在上传`);
    },
  },
};
</script>
<style lang="scss" scoped>
.el-input-number {
  width: 200px;
}
.box-card {
  margin-bottom: 18px;
}
.avatar-uploader {
  display: inline-block;
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
    object-fit: contain;
  }
}
::v-deep .filter-list-box {
  .wrapper {
    display: inline;
    vertical-align: top;
  }
  .upload-images {
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #c0ccda;
    border-radius: 6px;
    box-sizing: border-box;
    width: 100px;
    height: 100px;
    margin: 0 8px 8px 0;
    display: inline-block;
    position: relative;
    cursor: grabbing;
    .upload-image {
      width: 100%;
      height: 100%;
      display: -webkit-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      -webkit-justify-content: center;
      justify-content: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      -webkit-align-items: center;
      align-items: center;
      & > img {
        width: 100%;
      }
    }
    .upload-actions {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background-color: rgba(0, 0, 0, 0.5);
      text-align: center;
      display: none;
      i {
        margin-left: 6px;
        margin-top: 6px;
        &:first-child {
          margin-left: 0;
        }
      }
    }
    &:hover .upload-actions {
      display: block;
    }
    .upload-actions i {
      color: #fff;
      font-size: 18px;
      cursor: pointer;
    }
  }
}
.notice {
  margin: 10px 0 0 0;
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
}
</style>
