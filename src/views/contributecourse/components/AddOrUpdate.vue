<template>
  <el-dialog top="30px" width="60%" :title="form.id ? $t('table.edit') : $t('table.add') " :visible.sync="visible" :close-on-click-modal="false" :close-on-press-escape="false" @closed="onClose()">
    <el-form ref="form" :model="form" :rules="rules" label-width="160px">
   

      <el-form-item label="审核状态" prop="is_check">
        <el-select v-model="form.is_check"  placeholder="请选择审核状态">
          <el-option
              v-for="(item, index) in cateOptions"
              :disabled="item.disabled"
              :key="index"
              :label="item.label"
              :value="item.value"
          >
            <span>{{ item.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="失败原因" v-if="form.is_check==3" prop="errors">
          <el-input v-model="form.errors"  placeholder="失败原因"  />
      </el-form-item>
     
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="btnLoading" @click="onFormSubmit()">
        {{ $t('table.confirm') }}
      </el-button>
      <el-button @click="visible = false">
        {{ $t('table.cancel') }}
      </el-button>
    </div>

    <el-image-viewer
        v-if="imageViewer"
        :z-index="3000"
        :on-close="closeViewer"
        :url-list="imageViewerList"
    />

    <edit-tinymce
        v-if="editTinymceVisible"
        ref="editTinymce"
        @info="onInfo"
    />
  </el-dialog>
</template>

<script>
import CustomUpload from '@/components/Upload/CustomUpload'

import { addOrUpdate } from '@/api/contcourse'

import { DominKey, getToken } from '@/utils/auth'
import EditTinymce from './EditTinymce'
import draggable from 'vuedraggable'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import {authorList,cateList} from "@/api/user";
export default {
  name: 'AddOrUpdate',
  components: { CustomUpload, EditTinymce, ElImageViewer, draggable },
  data() {
    return {
      visible: false,
      btnLoading: false,
      editTinymceVisible: false,
      imageViewer: false,
      imageViewerList: [],
      domin: getToken(DominKey),
      currentName: '',
      // 作者
      authorOptions:[],
      cateOptions:[
        { label: '进行中', value: 1 , disable:true},
        { label: '通过', value: 2  , disable:false},
        { label: '未通过', value: 3  , disable:false}
      ],
      form: {
        name: '',
        author: '',
        intro: '',
        explain: '',
        images: '',
        sz_course_id: '',
        is_publish: 0,
        is_integral: 0,
        integral_num: 0,
        give_integral: 0,
        price: 0,
        send: 0,
        market_price: 0,
      },
      rules: {
        is_check: [
          { required: true, message: '请选择审核状态', trigger: ['blur', 'change'] }
        ],
        errors: [
          { required: true, message: '请输入未通过原因', trigger: ['blur', 'change'] }
        ],
      
      }
    }
  },
  computed: {
    dragOptions() {
      return {
        animation: 200,
        group: 'description',
        disabled: false,
        ghostClass: 'ghost'
      }
    }
  },
  methods: {
    init(data) {
      this.visible = true
      if (data) {
        this.form = data
      }
    },
    getDetails() {
      details(this.form.id)
          .then(response => {
            this.form = response.data
          })
          .catch(() => {})
    },
    onFormSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id) {
            this.$confirm(`是否提交审核?`, '审核', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
                .then(() => {
                  this.hanldaddOrUpdate()
                })
                .catch(() => {
                })
          } else {
            this.hanldaddOrUpdate()
          }
        }
      })
    },
    hanldaddOrUpdate() {
      this.form.issuer_id = this.form.issuer_id || 0
      const data = JSON.parse(JSON.stringify(this.form))
      this.btnLoading = true
      addOrUpdate(data, 'course')
          .then(({ msg }) => {
            this.$message.success(msg)
            this.visible = false
            this.$emit('refreshList')
          })
          .catch(() => {
            this.btnLoading = false
          })
    },
    onTinymce(data) {
      this.editTinymceVisible = true
      this.$nextTick(() => {
        this.$refs.editTinymce && this.$refs.editTinymce.init(data)
      })
    },
    onInfo(value) {
      this.form.intro = value
    },
    onClose() {
      this.$reset()
    },
    closeViewer() {
      this.imageViewer = false
    },
    elProgress(p) {
    },
    handleAvatarSuccess(response, file) {
      this.form[this.currentName] = response.name
    },
 
    cateLists() {
  
    },
    handleError() {
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件，请删除后在上传`)
    }
  }
}
</script>
<style lang="scss" scoped>
.el-input-number {
  width: 200px;
}
.box-card {
  margin-bottom: 18px;
}
.avatar-uploader {
  display: inline-block;
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
    object-fit: contain;
  }
}
::v-deep .filter-list-box {
  .wrapper {
    display: inline;
    vertical-align: top;
  }
  .upload-images {
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #c0ccda;
    border-radius: 6px;
    box-sizing: border-box;
    width: 100px;
    height: 100px;
    margin: 0 8px 8px 0;
    display: inline-block;
    position: relative;
    cursor: grabbing;
    .upload-image {
      width: 100%;
      height: 100%;
      display: -webkit-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      -webkit-justify-content: center;
      justify-content: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      -webkit-align-items: center;
      align-items: center;
      & > img {
        width: 100%;
      }
    }
    .upload-actions {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background-color: rgba(0,0,0,0.5);
      text-align: center;
      display: none;
      i {
        margin-left: 6px;
        margin-top: 6px;
        &:first-child {
          margin-left: 0;
        }
      }
    }
    &:hover .upload-actions {
      display: block;
    }
    .upload-actions i {
      color: #fff;
      font-size: 18px;
      cursor: pointer;
    }
  }
}
.notice {
  margin: 10px 0 0 0;
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
}
</style>
