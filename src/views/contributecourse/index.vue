<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" :model="search">
        <el-form-item label="关键词">
          <el-input
            v-model="search.keywords"
            style="width: 300px"
            placeholder="课程名称"
            clearable
            @clear="getList(1)"
            @keyup.enter.native="getList(1)"
          />
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select v-model="search.is_check" clearable @change="getList(1)">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-button icon="el-icon-search" @click="getList(1)">
          {{ $t("table.search") }}
        </el-button>
      </el-form>
    </div>
    <el-table v-loading="loading" border highlight-current-row :data="list">
      <el-table-column prop="id" label="ID" width="55" align="center" />
      <el-table-column width="120" label="封面图" header-align="center">
        <template slot-scope="{ row }">
          <div class="info-wrapper">
            <el-image
              class="image-item"
              :src="row.images && domin + row.images"
              :preview-src-list="[domin + row.images]"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline" />
              </div>
            </el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column min-width="140" label="名称" header-align="center">
        <template slot-scope="{ row }">
          {{ row.name }}
        </template>
      </el-table-column>
      <el-table-column min-width="140" label="标签" header-align="center">
        <template slot-scope="{ row }">
          <el-tag v-for="(item, i) in row.tag_list_info" :key="i">
            {{ item.tag }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="推荐人" width="100" header-align="center">
        <template slot-scope="{ row }">
          {{ row.suggest_user ? row.suggest_user.name : "平台" }}
        </template>
      </el-table-column>
      <el-table-column label="基础信息" min-width="280" header-align="center">
        <template slot-scope="{ row }">
          <div>价格(光子)：{{ row.price || 0 }}</div>
          <div>
            分类：<el-tag>
              {{
                row.cate
                  ? row.cate.parent_category
                    ? row.cate.parent_category.name + " / " + row.cate.name
                    : row.cate.name
                  : "-"
              }}
            </el-tag>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        width="150"
        prop="created_at"
        label="提交时间"
        header-align="center"
      />

      <el-table-column label="审核状态" min-width="100" header-align="center">
        <template slot-scope="{ row }">
          <div v-if="(row.is_check == 1 || !row.is_check) && row.errors">
            <el-button type="">再次投稿</el-button>
            <span v-if="row.errors">失败原因: {{ row.errors }}</span>
          </div>
          <div v-if="(row.is_check == 1 || !row.is_check) && !row.errors">
            <el-button type="">进行中</el-button>
          </div>

          <div v-if="row.is_check == 2">
            <el-button type="primary">已通过</el-button>
          </div>
          <div v-if="row.is_check == 3">
            <el-button type="danger">未通过</el-button> <br />
            失败原因: {{ row.errors }}
          </div>
        </template>
      </el-table-column>

      <el-table-column
        width="85"
        prop="dashang"
        label="打赏"
        header-align="center"
      />
      <el-table-column label="操作" width="250" align="center" fixed="right">
        <template slot-scope="{ row, $index }">
          <el-button-group>
            <el-button
              type="primary"
              v-if="
                row.is_check == 2 && (row.dashang == null || row.dashang == '')
              "
              @click="dashang(row)"
              >打赏</el-button
            >
            <el-button type="primary" @click="onCourseDetail(row)"
              >详情</el-button
            >
            <el-button type="primary" @click="onCourseDetailView(row)"
              >介绍</el-button
            >
            <el-button
              v-if="row.is_check != 2"
              type="primary"
              @click="onAddOrUpdate(row)"
              >审核</el-button
            >
            <el-button
              v-if="row.is_check != 2"
              type="danger"
              @click="onDelete(row, $index)"
              >删除</el-button
            >
          </el-button-group>
          <br />
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="pages.total > 0"
      :total="pages.total"
      :page.sync="pages.current"
      :limit.sync="pages.limit"
      @pagination="getList()"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshList="getList()"
    />
    <!--课程详情-->
    <course-detail
      v-if="courseDetailVisible"
      ref="courseDetail"
      @refreshList="getList()"
    />
    <!-- 弹窗, 新增 / 修改 -->
    <dashang v-if="dashangVisible" ref="dashang" @refreshList="getList()" />
    <el-dialog
      top="30px"
      width="60%"
      :title="'介绍'"
      :visible.sync="jieshaoVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @closed="onClose()"
    >
      <div v-html="courseIntro"></div>
    </el-dialog>
  </div>
</template>

<script>
import { dataList, contDelete } from "@/api/contcourse";
import { getToken, DominKey } from "@/utils/auth";
import { pickerOptions } from "@/utils/explain";
import { Swiper, SwiperSlide } from "vue-awesome-swiper";
import Pagination from "@/components/Pagination";
import AddOrUpdate from "./components/AddOrUpdate";
import "swiper/swiper-bundle.css";
import CourseDetail from "./components/CourseDetail";
import dashang from "./components/dashang";

import Comment from "../comment/Comment";

export default {
  name: "CourseList",
  components: {
    CourseDetail,
    AddOrUpdate,
    Pagination,
    Comment,
    Swiper,
    SwiperSlide,
    dashang,
  },
  data() {
    return {
      jieshaoVisible: false,
      courseIntro: "",
      swiperOption: {
        slidesPerView: 3,
        spaceBetween: 10,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
      },
      dashangVisible: true,
      domin: getToken(DominKey),
      search: {
        keywords: "",
        is_check: "",
      },
      pages: {
        total: 0,
        limit: 20,
        current: 1,
      },
      list: [],
      dateRangeValue: [],
      addOrUpdateVisible: false,
      courseDetailVisible: false,
      commentVisible: false,
      loading: false,
      pickerOptions,
      statusOptions: [
        { label: "全部", value: "" },
        { label: "进行中", value: 1 },
        { label: "再次投稿", value: 1 },
        { label: "已通过", value: 2 },
        { label: "未通过", value: 3 },
      ],
    };
  },
  computed: {
    swiper() {
      return function (v = 0) {
        return this.$refs[`mySwiper${v}`].$swiper;
      };
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getList();
    },
    getList(page = this.pages.current, loading = true) {
      if (this.loading) return;
      this.loading = loading;
      dataList({ page, ...this.search, limit: this.pages.limit }, "course")
        .then((response) => {
          if (response.code !== 200) return;
          this.list = response.data.data;
          this.pages.total = response.data.total;
        })
        .catch((error) => {
          this.$message.error(error.msg);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    dashang(data) {
      this.dashangVisible = true;
      this.$nextTick(() => {
        console.log(this.dashangVisible, "==============");
        this.$refs.dashang && this.$refs.dashang.init(data);
      });
    },
    onAddOrUpdate(data) {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate && this.$refs.addOrUpdate.init(data);
      });
    },
    onClose() {
      this.jieshaoVisible = false;
    },
    onCourseDetailView(data) {
      this.jieshaoVisible = true;
      this.courseIntro = data.intro;
    },
    onCourseDetail(data) {
      data.detailList = JSON.parse(data.detail);
      this.courseDetailVisible = true;
      this.$nextTick(() => {
        this.$refs.courseDetail && this.$refs.courseDetail.init(data);
      });
    },
    prev(index) {
      this.swiper(index).slidePrev();
    },
    next(index) {
      this.swiper(index).slideNext();
    },
    onComment(data) {
      this.commentVisible = true;
      data.target_type = "task";
      this.$nextTick(() => {
        this.$refs.comment && this.$refs.comment.init(data);
      });
    },
    onDelete(row, index) {
      this.$confirm(`确定进行[删除]操作?`, "删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
        cancelButtonClass: "btn-custom-cancel",
      })
        .then(() => {
          console.log(row.id, "del---------");
          contDelete(row.id, "course")
            .then(({ msg = "删除成功" }) => {
              this.$message.success(msg);
              this.getList();
            })
            .catch(() => {});
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .image-slot {
  font-size: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
}
.images-list {
  width: 80px;
  height: 80px;
}
.images-list .image-item {
  height: 100%;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
}
::v-deep .images-list .image-item img {
  height: auto;
}
.swiper-button-prev,
.swiper-button-next {
  display: none;
}
.recommendPage:hover .swiper-button-prev,
.recommendPage:hover .swiper-button-next {
  /*display: block;*/
}
::v-deep .detail-content img {
  width: 100%;
  height: auto;
}
::v-deep .el-image-viewer__canvas {
  width: 80%;
  margin: 0 auto;
}
::v-deep .edit-input {
  padding-right: 50px;
}
::v-deep .cancel-btn {
  position: absolute;
  right: 15px;
  top: 50%;
  margin-top: -14px;
}
::v-deep .edit-input .el-input-number__decrease,
::v-deep .edit-input .el-input-number__increase {
  display: none;
}
::v-deep .edit-input.is-controls-right .el-input__inner {
  padding: 0;
}

.recommend-page {
  width: 291px;
  margin: 0 auto;
  .swiper-container {
    user-select: none;
  }
  .swiper-button-prev,
  .swiper-button-next {
    display: none;
    pointer-events: auto;
    cursor: pointer;
  }
  .swiper-button-prev::after,
  .swiper-button-next::after {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }
  &:hover .swiper-button-prev,
  &:hover .swiper-button-next {
    display: flex;
  }
  .images-list {
    width: 80px;
    height: 80px;
    .image-item {
      height: 100%;
      cursor: pointer;
      display: -webkit-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      -webkit-justify-content: center;
      justify-content: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      -webkit-align-items: center;
      align-items: center;
      img {
        height: auto;
      }
    }
  }
}
.el-tag {
  margin-right: 4px;
}
</style>
