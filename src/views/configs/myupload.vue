<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="180px">
      <el-form-item label="首页介绍" prop="service">
        <tinymce v-model="form.desc" :height="100" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="btnLoading" @click="onFormSubmit()">
          {{ $t('table.confirm') }}
        </el-button>
      </el-form-item>
         <template>
        <el-form-item label="封面" prop="file">
          <custom-upload
              class-name="avatar-uploader"
              ref-name="image"
              accept="image/*"
              @handleBeforeUpload="beforeAvatarUpload"
              @handleSuccess="handleAvatarSuccesses"
          >
            <img v-if="form.image" :src="domin + form.image" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </custom-upload>
        </el-form-item>
      </template>
      <el-form-item label="标题" prop="wechat">
        <el-input v-model="form.title" placeholder="标题" clearable />
      </el-form-item>
      <el-form-item label="介绍" prop="wechat">
        <el-input v-model="form.url" placeholder="介绍" clearable />
      </el-form-item>
      <el-form-item label="下载链接" prop="wechat">
        <el-input v-model="form.itemdesc" placeholder="下载链接" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="btnLoading" @click="onFormSubmit()">
          {{ $t('table.confirm') }}
        </el-button>
      </el-form-item>
    </el-form>

  </div>
</template>

<script>
import {myuploadconfig, putmyuploadconfig} from '@/api/configs'
import Tinymce from '@/components/Tinymce'
import CustomUpload from '@/components/Upload/CustomUpload'
import {DominKey, getToken} from "@/utils/auth";

export default {
  name: 'myupload',
  components: { Tinymce, CustomUpload },
  data() {
    return {
      btnLoading: false,
      domin: getToken(DominKey),
      form: {
        desc: '',
        image: '',
        title: '',
        itemdesc: '',
        url: '',
      },
      rules: {
        desc: [
          { required: true, message: '下载声明内容不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    beforeAvatarUpload(file, cb) {
      const type = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
      const isLt2M = file.size / 1024 / 1024 < 20
      if (!type.includes(file.type)) {
        this.$message.error('上传图片只能是 ' + type.join(',') + ' 格式!')
        cb(false)
        return
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 20M')
        cb(false)
        return
      }
      cb(true)
    },
    handleAvatarSuccesses(response, file) {
      console.log(response, 'response ------');
      this.form.image = response.name
    },
    init() {
      this.getList()
    },
    getList() {
      myuploadconfig()
        .then(response => {
          this.form = response.data
        })
        .catch(({ msg = '加载失败' }) => {
          this.$message.error(msg)
        })
    },
    onFormSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.btnLoading = true
          putmyuploadconfig(this.form)
            .then(({ msg }) => {
              this.$message.success(msg)
              this.getList()
            })
            .catch(() => {
            })
            .finally(() => {
              this.btnLoading = false
            })
        }
      })
    }
  }
}
</script>

<style scoped  lang="scss">
.app-container {
  width: 800px;
}
.el-input-number {
  width: 200px;
}
.box-card {
  margin-bottom: 18px;
}
::v-deep.avatar-uploader {
  display: inline-block;
  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
    object-fit: contain;
  }
}
.notice {
  margin: 10px 0 0 0;
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
}
.notice {
  color: #909399;
  font-size: 12px;
}
.el-input-number {
  width: 200px;
}
.app-container {
  width: 800px;
}
</style>
