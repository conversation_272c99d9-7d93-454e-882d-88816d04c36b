<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-form-item class="skus-list" label="友情链接" prop="form">
        <div v-for="(item, index) in form" :key="index">
          <div class="skus-item">
            <div class="sku-name">名称：{{ item.name }}</div>
          </div>
          <div class="skus-item">
            <div class="sku-name">链接：{{ item.name }}</div>
          </div>
        </div>
        <div>
          <span class="sku-name">添加链接</span>
          <el-input
              v-model.trim="temp.name"
              class="addskus middle"
              placeholder="请输入名称"
              clearable
          >
            <template slot="prepend">名称</template>
          </el-input>
          -
          <el-input
              v-model.trim="temp.value"
              class="addskus middle"
              placeholder="请输入链接"
              clearable
          >
            <template slot="prepend">链接</template>
          </el-input>
          <el-button type="primary" class="sonvalue" :disabled="form.is_change_stock?false:true" @click="onAddSku">添加</el-button>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="btnLoading" @click="onFormSubmit()">
          {{ $t('table.confirm') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { friend, putFriend } from '@/api/configs'
export default {
  name: 'Friend',
  data() {
    return {
      btnLoading: false,
      form: {
        email: '',
        qq: '',
        wechat: '',
        phone: '',
      },
      temp: {
        name: '',
        link: ''
      },
      rules: {
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.getList()
    },
    getList() {
      friend()
        .then(response => {
          this.form = response.data.value
        })
        .catch(() => {})
    },
    onFormSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.btnLoading = true
          putFriend(this.form)
            .then(({ msg }) => {
              this.$message.success(msg)
              this.getList()
            })
            .catch(() => {})
            .finally(() => {
              this.btnLoading = false
            })
        }
      })
    },
    onAddSku() {
      this.form.push({ name: this.temp.name, link: this.temp.link })
      this.temp = { name: '', link: '' }
    },
  }
}
</script>

<style lang="scss"  scoped>
.notice {
  color: #909399;
  font-size: 12px;
}
.el-input-number {
  width: 200px;
}
.app-container {
  width: 800px;
}
.skus-list {
  .el-form-item__content > div {
  .skus-item:first-child {
    margin-top: 0;
  }
  .skus-item {
    display: flex;
    margin: 12px 0;
  }
  }
  .sku-name {
    display: inline-block;
    width: 64px;
    flex-shrink: 0;
  }
  .sonTag {
    margin-right: 5px;
    margin-bottom: 5px;
  }
  .addskus {
    width: 200px;
  }
  .middle {
    vertical-align: middle;
  }
  .sonvalue {
    margin-left: 5px;
  }
}
</style>
