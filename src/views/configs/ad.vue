<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <template>
        <el-form-item label="广告图" prop="file">
          <custom-upload
              class-name="avatar-uploader"
              ref-name="file"
              @handleBeforeUpload="beforeAvatarUpload"
              @handleSuccess="handleAvatarSuccesses"
          >
            <img v-if="form.file" :src="domin + form.file" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </custom-upload>
        </el-form-item>
      </template>
      <el-form-item label="广告链接" prop="wechat">
        <el-input v-model="form.link" placeholder="广告链接" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="btnLoading" @click="onFormSubmit()">
          {{ $t('table.confirm') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {ad, putAd} from '@/api/configs'
import CustomUpload from '@/components/Upload/CustomUpload'
import EditTinymce from "@/views/shop/components/EditTinymce";
import {DominKey, getToken} from "@/utils/auth";
export default {
  name: 'Ad',
  components: { CustomUpload, EditTinymce },
  data() {
    return {
      btnLoading: false,
      domin: getToken(DominKey),
      form: {
        file: '',
        link: '',
      },
      rules: {
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.getList()
    },
    getList() {
      ad()
        .then(response => {
          this.form = response.data.value
        })
        .catch(() => {})
    },
    onFormSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.btnLoading = true
          putAd(this.form)
            .then(({ msg }) => {
              this.$message.success(msg)
              this.getList()
            })
            .catch(() => {})
            .finally(() => {
              this.btnLoading = false
            })
        }
      })
    },
    beforeAvatarUpload(file, cb) {
      const type = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
      const isLt2M = file.size / 1024 / 1024 < 20
      if (!type.includes(file.type)) {
        this.$message.error('上传图片只能是 ' + type.join(',') + ' 格式!')
        cb(false)
        return
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 20M')
        cb(false)
        return
      }
      cb(true)
    },
    handleAvatarSuccesses(response, file) {
      this.form.file = response.name
    },
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  width: 800px;
}
.el-input-number {
  width: 200px;
}
.box-card {
  margin-bottom: 18px;
}
.avatar-uploader {
  display: inline-block;
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
    object-fit: contain;
  }
}
.notice {
  margin: 10px 0 0 0;
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
}
</style>
