<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="180px">
      <el-form-item label="服务条款" prop="service">
        <tinymce v-model="form.service" :height="400" :width="800" />
<!--        <el-input v-model="form.service" type="textarea" :rows="10" placeholder="服务条款" clearable />-->
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="btnLoading" @click="onFormSubmit()">
          {{ $t('table.confirm') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {service, putService} from '@/api/configs'
import Tinymce from '@/components/Tinymce'

export default {
  name: 'Service',
  components: { Tinymce },
  data() {
    return {
      btnLoading: false,
      form: {
        service: ''
      },
      rules: {
        service: [
          { required: true, message: '服务条款内容不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.getList()
    },
    getList() {
      service()
        .then(response => {
          this.form.service = response.data.value
        })
        .catch(({ msg = '加载失败' }) => {
          this.$message.error(msg)
        })
    },
    onFormSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.btnLoading = true
          putService(this.form)
            .then(({ msg }) => {
              this.$message.success(msg)
              this.getList()
            })
            .catch(() => {
            })
            .finally(() => {
              this.btnLoading = false
            })
        }
      })
    }
  }
}
</script>

<style scoped>
.notice {
  color: #909399;
  font-size: 12px;
}
.el-input-number {
  width: 200px;
}
.app-container {
  width: 800px;
}
</style>
