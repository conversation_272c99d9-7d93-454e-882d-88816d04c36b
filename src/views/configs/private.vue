<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="180px">
      <el-form-item label="隐私协议" prop="private">
        <tinymce v-model="form.private" :height="400" :width="800" />
<!--        <el-input v-model="form.private" type="textarea" :rows="10" placeholder="隐私协议" clearable />-->
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="btnLoading" @click="onFormSubmit()">
          {{ $t('table.confirm') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {private1, putPrivate1} from '@/api/configs'
import Tinymce from '@/components/Tinymce'

export default {
  name: 'Private',
  components: { Tinymce },
  data() {
    return {
      btnLoading: false,
      form: {
        private: ''
      },
      rules: {
        private: [
          { required: true, message: '协议内容不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.getList()
    },
    getList() {
      private1()
        .then(response => {
          this.form.private = response.data.value
        })
        .catch(({ msg = '加载失败' }) => {
          this.$message.error(msg)
        })
    },
    onFormSubmit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.btnLoading = true
          putPrivate1(this.form)
            .then(({ msg }) => {
              this.$message.success(msg)
              this.getList()
            })
            .catch(() => {
            })
            .finally(() => {
              this.btnLoading = false
            })
        }
      })
    }
  }
}
</script>

<style scoped>
.notice {
  color: #909399;
  font-size: 12px;
}
.el-input-number {
  width: 200px;
}
.app-container {
  width: 800px;
}
</style>
