import request from '@/utils/request'

// 用户列表
export function dataList(params) {
  return request.get('/users', {
    params
  })
}

// 用户权限树
export function dataCateTreeList(params) {
  return request.get('/users/cateTree', {
    params
  })
}

// 添加 / 修改
export function addOrUpdate({ id, ...data }) {
  let method = 'POST'
  let url = '/users'

  if (id > 0) {
    method = 'PUT'
    url += `/${id}`
    data.id = id
  }

  return request({ url, method, data })
}

// 删除
export function deleteData(id) {
  return request.delete(`/users/${id}/delete`)
}
// 资产清除
export function clearWallet(id) {
  return request.post(`/users/${id}/clear/wallet`)
}


// 用户收藏的
export function userCollection(id, params) {
  return request.get(`/users/course/list`, {
    params
  })
}

// 用户课程删除
export function userCourseDelete(id) {
  return request.delete(`/users/course/${id}/delete`)
}

// 导出用户信息
export function exportOrder(params) {
  return request.get('/users/export/info', {
    params
  })
}

export function authorList() {
  return request.get('/public/user/author')
}

export function userList() {
  return request.get('/public/user')
}

// 分类
export function cateList() {
  return request.get('/public/course/cate')
}

// 课程分类
export function courseList() {
  return request.get('/public/course/filter')
}
// 用户课程添加
export function addUserCourse({ id, ...data }) {
  let method = 'POST'
  let url = '/users/user_course'

  if (id > 0) {
    method = 'PUT'
    url += `/${id}`
    data.id = id
  }

  return request({ url, method, data })
}

