import request from '@/utils/request'

// 列表
export function dataList(params) {
  return request.get('/goods', {
    params
  })
}

// 添加 / 修改
export function addOrUpdate({ id, ...data }) {
  let method = 'POST'
  let url = '/goods'

  if (id > 0) {
    method = 'PUT'
    url += `/${id}`
    data.id = id
  }

  return request({ url, method, data })
}

// 删除
export function deleteData(id) {
  return request.delete(`/goods/${id}/delete`)
}

// 详情
export function details(id) {
  return request.get(`/goods/${id}`)
}

//  商品上下架
export function setStatus({ id, ...data }) {
  return request.post(`/goods/status/${id}`, {
    ...data
  })
}
// 分类
export function cateList() {
  return request.get('/public/goods/cate')
}


// 标签列表
export function getTagList(params) {
  return request.get('/tag/getList', {
    params
  })
}