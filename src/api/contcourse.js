import request from '@/utils/request'

// 列表
export function dataList(params, type) {
  return request.get('/contribution/'+type, {
    params
  })
}


// 删除
export function contDelete(id, type) {
  console.log(id, type);
  return request.delete(`/contribution/${type}/${id}/delete`)
}


// 审核
export function addOrUpdate({ id, ...data }, type) {
  let method = 'POST'
  let url = '/contribution/update/'+type

  if (id > 0) {
    method = 'PUT'
    url += `/${id}`
    data.id = id
  }
  return request({ url, method, data })
}

//打赏

export function addwallet({ id, ...data }, type) {
  let method = 'POST'
  let url = '/contribution/wallet/'+type
  console.log(url, 'url=========');
  if (id > 0) {
    method = 'PUT'
    url += `/${id}`
    data.id = id
  }
  return request({ url, method, data })
}





// 课程详情
export function details(id) {
  return request.get(`/courses/${id}`)
}


// 课程列表
export function getDetailLists(params) {
  return request.get(`/courses/detail/list`, {
    params
  })
}
// 课程详情删除
export function courseDelete(id) {
  return request.delete(`/courses/${id}/delete`)
}

// 章节
export function chapterList(id) {
  return request.get(`/courses/${id}/chapter/filter`)
}
// 课件详情
export function taskDetails(id) {
  return request.get(`/courses/detail/${id}`)
}
// 课程详情添加
export function detailAddOrUpdate({ id, ...data }) {
  let method = 'POST'
  let url = '/courses/detail'

  if (id > 0) {
    method = 'PUT'
    url += `/${id}`
    data.id = id
  }
  return request({ url, method, data })
}

// 深造课程列表
export function szDataList(params) {
  return request.get('/public/sz_course', {
    params
  })
}
