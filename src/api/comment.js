import request from '@/utils/request'

// 评论列表
export function commentsList({ ...params }) {
  return request.get(`/comments`, {
    params
  })
}
// 回复列表
export function replyList({ id, ...params }) {
  return request.get(`/comments/${id}/reply`, {
    params
  })
}
// 删除 `评论`
export function deleteComment(id) {
  return request.delete(`/comments/${id}`)
}
// 删除 `回复`
export function deleteReply(id) {
  return request.delete(`/comments/replys/${id}`)
}
