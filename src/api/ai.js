import request from '@/utils/request'

// 列表
export function newsList(params) {
  return request.get('/ai/news', {
    params
  })
}

// 详情
export function newsDetails(id) {
  return request.get(`/ai/news/${id}`)
}

// 添加 / 修改
export function newsAddOrUpdate({ id, ...data }) {
  let method = 'POST'
  let url = '/ai/news'

  if (id > 0) {
    method = 'PUT'
    url += `/${id}`
    data.id = id
  }
  return request({ url, method, data })
}


// 删除
export function newsDelete(id) {
  return request.delete(`/ai/news/${id}/delete`)
}

// 列表
export function galleryList(params) {
  return request.get('/ai/gallery', {
    params
  })
}

// 详情
export function galleryDetails(id) {
  return request.get(`/ai/gallery/${id}`)
}

// 添加 / 修改
export function galleryAddOrUpdate({ id, ...data }) {
  let method = 'POST'
  let url = '/ai/gallery'

  if (id > 0) {
    method = 'PUT'
    url += `/${id}`
    data.id = id
  }
  return request({ url, method, data })
}


// 删除
export function galleryDelete(id) {
  return request.delete(`/ai/gallery/${id}/delete`)
}


// 列表
export function galleryCategoryList(params) {
  return request.get('/ai/gallery_category', {
    params
  })
}

// 详情
export function galleryCategoryDetails(id) {
  return request.get(`/ai/gallery_category/${id}`)
}

// 添加 / 修改
export function galleryCategoryAddOrUpdate({ id, ...data }) {
  let method = 'POST'
  let url = '/ai/gallery_category'

  if (id > 0) {
    method = 'PUT'
    url += `/${id}`
    data.id = id
  }
  return request({ url, method, data })
}


// 删除
export function galleryCategoryDelete(id) {
  return request.delete(`/ai/gallery_category/${id}/delete`)
}

// 分类映射
export function galleryCategoryFilterList(param) {
  return request.get('/public/gallery_category/filter', {
    ...param
  })
}


// 列表
export function hotList(params) {
  return request.get('/ai/hot', {
    params
  })
}

// 详情
export function hotDetails(id) {
  return request.get(`/ai/hot/${id}`)
}

// 添加 / 修改
export function hotAddOrUpdate({ id, ...data }) {
  let method = 'POST'
  let url = '/ai/hot'

  if (id > 0) {
    method = 'PUT'
    url += `/${id}`
    data.id = id
  }
  return request({ url, method, data })
}


// 删除
export function hotDelete(id) {
  return request.delete(`/ai/hot/${id}/delete`)
}


// 列表
export function renderStyleList(params) {
  return request.get('/ai/render_style', {
    params
  })
}

// 详情
export function renderStyleDetails(id) {
  return request.get(`/ai/render_style/${id}`)
}

// 添加 / 修改
export function renderStyleAddOrUpdate({ id, ...data }) {
  let method = 'POST'
  let url = '/ai/render_style'

  if (id > 0) {
    method = 'PUT'
    url += `/${id}`
    data.id = id
  }
  return request({ url, method, data })
}


// 删除
export function renderStyleDelete(id) {
  return request.delete(`/ai/render_style/${id}/delete`)
}

// 分类映射
export function renderStyleFilterList(param) {
  return request.get('/public/render_style/filter', {
    ...param
  })
}

// 插件详情
export function plugDetails(id) {
  return request.get(`/ai/plug/${id}`)
}

// 添加 / 修改
export function plugAddOrUpdate({ id, ...data }) {
  let method = 'POST'
  let url = '/ai/plug'

  if (id > 0) {
    method = 'PUT'
    url += `/${id}`
    data.id = id
  }
  return request({ url, method, data })
}

// 详情列表
export function getPlugDetailLists(params) {
  return request.get(`/ai/plug/detail/list`, {
    params
  })
}

// 详情删除
export function plugDetailDelete(id) {
  return request.delete(`/ai/plug/detail/${id}/delete`)
}

// 插件详情
export function taskDetails(id) {
  return request.get(`/ai/plug/detail/${id}`)
}
// 详情添加
export function plugDetailAddOrUpdate({ id, ...data }) {
  let method = 'POST'
  let url = '/ai/plug/detail'

  if (id > 0) {
    method = 'PUT'
    url += `/${id}`
    data.id = id
  }
  return request({ url, method, data })
}

// VIP配置详情
export function vipSettingDetails(id) {
  return request.get(`/ai/vip_setting/${id}`)
}

// 添加 / 修改
export function vipSettingAddOrUpdate({ id, ...data }) {
  let method = 'POST'
  let url = '/ai/vip_setting'

  if (id > 0) {
    method = 'PUT'
    url += `/${id}`
    data.id = id
  }
  return request({ url, method, data })
}

// 列表
export function vipList(params) {
  return request.get('/ai/vip', {
    params
  })
}

// 详情
export function vipDetails(id) {
  return request.get(`/ai/vip/${id}`)
}

// 添加 / 修改
export function vipAddOrUpdate({ id, ...data }) {
  let method = 'POST'
  let url = '/ai/vip'

  if (id > 0) {
    method = 'PUT'
    url += `/${id}`
    data.id = id
  }
  return request({ url, method, data })
}


// 删除
export function vipDelete(id) {
  return request.delete(`/ai/vip/${id}/delete`)
}
