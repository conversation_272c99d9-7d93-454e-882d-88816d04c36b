import request from '@/utils/request'

// 列表
export function dataList(params) {
  return request.get('/tag/getList', {
    params
  })
}


// 删除
export function contDelete(id) {
  return request.delete(`/tag/${id}/delete`)
}


// 审核
export function addOrUpdate({ id, ...data }) {
  let method = 'POST'
  let url = '/tag/poststore'

  if (id > 0) {
    method = 'PUT'
    data.id = id
  }
  return request({ url, method, data })
}




