import request from '@/utils/request'

// 联系我们
export function contact() {
  return request.get('configs/contact')
}
export function putContact(data) {
  return request.put('configs/contact', data)
}

// 关于我们
export function about() {
  return request.get('/configs/about')
}
export function putAbout(data) {
  return request.put('/configs/about', {
    ...data
  })
}

// 注册协议
export function register() {
  return request.get('/configs/register')
}
export function putRegister(data) {
  return request.put('/configs/register', {
    ...data
  })
}

// 隐私协议
export function private1() {
  return request.get('/configs/private')
}
export function putPrivate1(data) {
  return request.put('/configs/private', {
    ...data
  })
}

// 服务条款
export function service() {
  return request.get('/configs/service')
}
export function putService(data) {
  return request.put('/configs/service', {
    ...data
  })
}

// 版权声明
export function copyrightStatement() {
  return request.get('/configs/copyright_statement')
}
export function putCopyrightStatement(data) {
  return request.put('/configs/copyright_statement', {
    ...data
  })
}

// 上传声明
export function uploadStatement() {
  return request.get('/configs/upload_statement')
}
export function putUploadStatement(data) {
  return request.put('/configs/upload_statement', {
    ...data
  })
}

// 下载声明
export function downloadStatement() {
  return request.get('/configs/download_statement')
}
export function putDownloadStatement(data) {
  return request.put('/configs/download_statement', {
    ...data
  })
}

// 常见问题
export function question() {
  return request.get('/configs/question')
}
export function putQuestion(data) {
  return request.put('/configs/question', {
    ...data
  })
}

// 广告
export function ad() {
  return request.get('configs/ad')
}
export function putAd(data) {
  return request.put('configs/ad', data)
}

// 广告
export function friend() {
  return request.get('configs/friend')
}
export function putFriend(data) {
  return request.put('configs/friend', data)
}



// 广告
export function myuploadconfig() {
  return request.get('configs/myuploadconfig')
}
export function putmyuploadconfig(data) {
  return request.put('configs/myuploadconfig', data)
}


// Ai服务器
export function aiServer() {
  return request.get('configs/aiServer')
}
export function putaiServer(data) {
  return request.put('configs/aiServer', data)
}


