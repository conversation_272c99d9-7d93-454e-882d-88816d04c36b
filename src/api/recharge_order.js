import request from '@/utils/request'

// 充值订单列表（基于接口文档）
export function dataList(params) {
  return request.get('/admin/recharge_order', {
    params
  })
}

// 导出充值订单
export function exportOrder(params) {
  return request.get('/admin/recharge_order/export', {
    params
  })
}

// 获取订单开票记录（基于接口文档）
export function getInvoiceRecords(orderId) {
  return request.get(`/admin/recharge_order/${orderId}/invoice-records`)
}

// 上传发票文件（基于接口文档）
export function uploadInvoice(data) {
  return request.post('/admin/recharge_order/invoice/upload', data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 更新开票状态（基于接口文档）
export function updateInvoiceStatus(invoiceId, data) {
  return request.put(`/admin/recharge_order/invoice/${invoiceId}/status`, data)
}
