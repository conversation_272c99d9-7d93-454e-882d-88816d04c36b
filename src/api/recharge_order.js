import request from '@/utils/request'

// 充值订单列表
export function dataList(params) {
  return request.get('/recharge_order', {
    params
  })
}

// 导出充值订单
export function exportOrder(params) {
  return request.get('/recharge_order/export', {
    params
  })
}

// 获取订单开票详情
export function getInvoiceDetail(orderId) {
  return request.get(`/recharge_order/${orderId}/invoice`)
}

// 上传发票文件
export function uploadInvoice(orderId, data) {
  return request.post(`/recharge_order/${orderId}/invoice/upload`, data)
}

// 下载发票文件
export function downloadInvoice(orderId, invoiceId) {
  return request.get(`/recharge_order/${orderId}/invoice/${invoiceId}/download`, {
    responseType: 'blob'
  })
}
