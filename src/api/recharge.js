import request from '@/utils/request'

// 管理员充值光子
export function adminRecharge(data) {
  return request.post('/recharge/admin-recharge', data)
}

// 获取充值记录列表
export function getRechargeList(params) {
  return request.get('/recharge/list', {
    params
  })
}

// 导出充值记录
export function exportRecharge(params) {
  return request.get('/recharge/export', {
    params
  })
}

// 获取充值统计
export function getRechargeStats(params) {
  return request.get('/recharge/stats', {
    params
  })
}
