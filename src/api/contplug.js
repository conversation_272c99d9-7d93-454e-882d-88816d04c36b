import request from '@/utils/request'

// 列表
export function dataList(params) {
  return request.get('/plugs', {
    params
  })
}
// 分类
export function cateList() {
  return request.get('/public/plug/cate')
}

// 课程详情
export function details(id) {
  return request.get(`/plugs/${id}`)
}

// 添加 / 修改
export function addOrUpdate({ id, ...data }) {
  let method = 'POST'
  let url = '/plugs'

  if (id > 0) {
    method = 'PUT'
    url += `/${id}`
    data.id = id
  }
  return request({ url, method, data })
}
// 详情删除
export function plugDelete(id) {
  return request.delete(`/plugs/${id}/delete`)
}

// 列表
export function getDetailLists(params) {
  return request.get(`/plugs/detail/list`, {
    params
  })
}

// 插件详情
export function taskDetails(id) {
  return request.get(`/plugs/detail/${id}`)
}
// 详情添加
export function detailAddOrUpdate({ id, ...data }) {
  let method = 'POST'
  let url = '/plugs/detail'

  if (id > 0) {
    method = 'PUT'
    url += `/${id}`
    data.id = id
  }
  return request({ url, method, data })
}
// 详情删除
export function plugDetailDelete(id) {
  return request.delete(`/plugs/detail/${id}/delete`)
}
