import request from '@/utils/request'

// 列表
export function dataList(params) {
  return request.get('/coupon_codes', {
    params
  })
}

// 详情
export function details(id) {
  return request.get(`/coupon_codes/${id}`)
}

// 添加 / 修改
export function addOrUpdate({ id, ...data }) {
  let method = 'POST'
  let url = '/coupon_codes'

  if (id > 0) {
    method = 'PUT'
    url += `/${id}`
    data.id = id
  }
  return request({ url, method, data })
}


// 删除
export function newDelete(id) {
  return request.delete(`/coupon_codes/${id}/delete`)
}
