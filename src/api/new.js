import request from '@/utils/request'

// 列表
export function dataList(params) {
  return request.get('/news', {
    params
  })
}

// 详情
export function details(id) {
  return request.get(`/news/${id}`)
}

// 添加 / 修改
export function addOrUpdate({ id, ...data }) {
  let method = 'POST'
  let url = '/news'

  if (id > 0) {
    method = 'PUT'
    url += `/${id}`
    data.id = id
  }
  return request({ url, method, data })
}


// 删除
export function newDelete(id) {
  return request.delete(`/news/${id}/delete`)
}




// 标签列表
export function getTagList(params) {
  return request.get('/tag/getList', {
    params
  })
}